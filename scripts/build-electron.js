#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Building Electron application...');

// Ensure frontend is built first
console.log('📦 Building frontend...');
try {
  execSync('node scripts/build-frontend.js', { stdio: 'inherit' });
} catch (error) {
  console.error('❌ Frontend build failed');
  process.exit(1);
}

// Build backend if needed (for production)
console.log('🔧 Preparing backend...');
const backendDistDir = path.join(__dirname, '../dist/backend');
if (!fs.existsSync(backendDistDir)) {
  fs.mkdirSync(backendDistDir, { recursive: true });
}

// For now, we'll copy the server files
// In production, you might want to bundle the backend as well
const serverContent = `
// Production server entry point
const { spawn } = require('child_process');
const path = require('path');

// Start the main server
const serverPath = path.join(__dirname, '../../base/cmd.ts');
const server = spawn('bun', ['run', serverPath, 'server'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production'
  }
});

server.on('error', (error) => {
  console.error('Server error:', error);
  process.exit(1);
});

server.on('exit', (code) => {
  console.log('Server exited with code:', code);
  process.exit(code);
});
`;

fs.writeFileSync(path.join(backendDistDir, 'server.js'), serverContent);

// Build Electron app
console.log('⚡ Building Electron application...');
try {
  execSync('electron-builder', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'production'
    }
  });
  console.log('✅ Electron build completed successfully!');
} catch (error) {
  console.error('❌ Electron build failed:', error.message);
  process.exit(1);
}
