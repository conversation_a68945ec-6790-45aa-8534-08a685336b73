#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🧪 Testing Electron Desktop Application Setup...\n');

// Test configuration
const tests = [
  {
    name: 'Dependencies Check',
    test: checkDependencies
  },
  {
    name: 'File Structure Validation',
    test: checkFileStructure
  },
  {
    name: 'Frontend Build Test',
    test: testFrontendBuild
  },
  {
    name: 'Electron Configuration Test',
    test: testElectronConfig
  }
];

// Track test results
let passed = 0;
let failed = 0;

async function runTests() {
  console.log('Starting Electron integration tests...\n');
  
  for (const test of tests) {
    try {
      console.log(`🔍 Running: ${test.name}`);
      await test.test();
      console.log(`✅ PASSED: ${test.name}\n`);
      passed++;
    } catch (error) {
      console.log(`❌ FAILED: ${test.name}`);
      console.log(`   Error: ${error.message}\n`);
      failed++;
    }
  }
  
  // Summary
  console.log('📊 Test Summary:');
  console.log(`   ✅ Passed: ${passed}`);
  console.log(`   ❌ Failed: ${failed}`);
  console.log(`   📈 Success Rate: ${Math.round((passed / (passed + failed)) * 100)}%\n`);
  
  if (failed === 0) {
    console.log('🎉 All tests passed! Electron setup is ready for development.');
  } else {
    console.log('⚠️  Some tests failed. Please review the errors above.');
    process.exit(1);
  }
}

async function checkDependencies() {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  
  // Check required dependencies
  const requiredDeps = ['electron', 'electron-builder', '@electron/packager'];
  const missingDeps = requiredDeps.filter(dep => 
    !packageJson.devDependencies[dep] && !packageJson.dependencies[dep]
  );
  
  if (missingDeps.length > 0) {
    throw new Error(`Missing dependencies: ${missingDeps.join(', ')}`);
  }
  
  // Check scripts
  const requiredScripts = ['electron:dev', 'electron:build', 'build:frontend'];
  const missingScripts = requiredScripts.filter(script => 
    !packageJson.scripts[script]
  );
  
  if (missingScripts.length > 0) {
    throw new Error(`Missing scripts: ${missingScripts.join(', ')}`);
  }
}

async function checkFileStructure() {
  const requiredFiles = [
    'electron/main.js',
    'electron/preload.js',
    'electron/package.json',
    'scripts/build-frontend.js',
    'scripts/build-electron.js',
    'scripts/dev-electron.js',
    'electron-builder.json'
  ];
  
  const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
  
  if (missingFiles.length > 0) {
    throw new Error(`Missing files: ${missingFiles.join(', ')}`);
  }
  
  // Check if main.js has required content
  const mainJs = fs.readFileSync('electron/main.js', 'utf8');
  const requiredContent = ['BrowserWindow', 'app.whenReady', 'contextIsolation'];
  const missingContent = requiredContent.filter(content => !mainJs.includes(content));
  
  if (missingContent.length > 0) {
    throw new Error(`Main.js missing required content: ${missingContent.join(', ')}`);
  }
}

async function testFrontendBuild() {
  return new Promise((resolve, reject) => {
    console.log('   Building frontend...');
    
    const buildProcess = spawn('npm', ['run', 'build:frontend'], {
      stdio: 'pipe'
    });
    
    let output = '';
    let errorOutput = '';
    
    buildProcess.stdout.on('data', (data) => {
      output += data.toString();
    });
    
    buildProcess.stderr.on('data', (data) => {
      errorOutput += data.toString();
    });
    
    buildProcess.on('close', (code) => {
      if (code === 0) {
        // Check if build artifacts exist
        const requiredArtifacts = [
          'dist/frontend/index.html',
          'dist/frontend/index.js'
        ];
        
        const missingArtifacts = requiredArtifacts.filter(file => !fs.existsSync(file));
        
        if (missingArtifacts.length > 0) {
          reject(new Error(`Build artifacts missing: ${missingArtifacts.join(', ')}`));
        } else {
          resolve();
        }
      } else {
        reject(new Error(`Build failed with code ${code}: ${errorOutput}`));
      }
    });
    
    buildProcess.on('error', (error) => {
      reject(new Error(`Build process error: ${error.message}`));
    });
  });
}

async function testElectronConfig() {
  // Test electron-builder configuration
  const config = JSON.parse(fs.readFileSync('electron-builder.json', 'utf8'));
  
  const requiredFields = ['appId', 'productName', 'directories', 'files'];
  const missingFields = requiredFields.filter(field => !config[field]);
  
  if (missingFields.length > 0) {
    throw new Error(`electron-builder.json missing fields: ${missingFields.join(', ')}`);
  }
  
  // Test package.json main field
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.main !== 'electron/main.js') {
    throw new Error('package.json main field should point to electron/main.js');
  }
}

// Run the tests
runTests().catch(error => {
  console.error('Test runner error:', error);
  process.exit(1);
});
