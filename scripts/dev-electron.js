#!/usr/bin/env node

const { spawn } = require('child_process');
const path = require('path');

console.log('🚀 Starting Electron development environment...');

// Start the backend server first
console.log('🔧 Starting backend server...');
const backendProcess = spawn('bun', ['run', 'base/cmd.ts', 'server'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'development',
    PORT: '3000',
    HOST: 'localhost'
  }
});

// Wait a moment for the backend to start
setTimeout(() => {
  console.log('⚡ Starting Electron...');
  
  // Start Electron
  const electronProcess = spawn('electron', [path.join(__dirname, '../electron/main.js')], {
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'development',
      BACKEND_PORT: '3000',
      BACKEND_HOST: 'localhost'
    }
  });

  electronProcess.on('close', () => {
    console.log('🛑 Electron closed, shutting down backend...');
    backendProcess.kill();
    process.exit(0);
  });

  electronProcess.on('error', (error) => {
    console.error('❌ Electron error:', error);
    backendProcess.kill();
    process.exit(1);
  });

}, 3000);

backendProcess.on('error', (error) => {
  console.error('❌ Backend server error:', error);
  process.exit(1);
});

// Handle process termination
process.on('SIGINT', () => {
  console.log('\n🛑 Shutting down development environment...');
  backendProcess.kill();
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Shutting down development environment...');
  backendProcess.kill();
  process.exit(0);
});
