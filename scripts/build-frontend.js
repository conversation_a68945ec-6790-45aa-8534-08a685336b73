#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🏗️  Building frontend for Electron...');

// Create dist directory
const distDir = path.join(__dirname, '../dist');
const frontendDistDir = path.join(distDir, 'frontend');

if (!fs.existsSync(distDir)) {
  fs.mkdirSync(distDir, { recursive: true });
}

if (!fs.existsSync(frontendDistDir)) {
  fs.mkdirSync(frontendDistDir, { recursive: true });
}

// Copy frontend files
const frontendSrcDir = path.join(__dirname, '../mod/frontend');

// Copy HTML file and modify for production
const htmlContent = fs.readFileSync(path.join(frontendSrcDir, 'index.html'), 'utf8');
const modifiedHtml = htmlContent
  .replace('src="./index.tsx"', 'src="./index.js"')
  .replace('href="tailwindcss"', 'href="./styles.css"');

fs.writeFileSync(path.join(frontendDistDir, 'index.html'), modifiedHtml);

// For now, we'll use a simple approach - copy the source files
// In a real production setup, you'd want to use a bundler like esbuild, webpack, or vite

console.log('📦 Bundling React application...');

try {
  // Use Bun to bundle the frontend
  execSync(`bun build ${path.join(frontendSrcDir, 'index.tsx')} --outdir ${frontendDistDir} --target browser --format esm --splitting --minify`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });

  // Bundle CSS
  execSync(`bun build ${path.join(frontendSrcDir, 'styles/app.css')} --outfile ${path.join(frontendDistDir, 'styles.css')} --minify`, {
    stdio: 'inherit',
    cwd: path.join(__dirname, '..')
  });

  console.log('✅ Frontend build completed successfully!');
} catch (error) {
  console.error('❌ Frontend build failed:', error.message);
  process.exit(1);
}
