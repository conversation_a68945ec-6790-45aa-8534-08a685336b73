name: "electric_quickstart"

services:
  postgres:
    env_file: &env
      - path: .env
        required: true
      - path: .env.local
        required: false

    image: postgres:16-alpine
    environment:
      - POSTGRES_DB
      - POSTGRES_USER
      - POSTGRES_PASSWORD
    ports:
      - ${POSTGRES_PORT}:5432
    tmpfs:
      - /var/lib/postgresql/data
      - /tmp
    command:
      - -c
      - listen_addresses=*
      - -c
      - wal_level=logical

  electric:
    env_file:
      - path: .env
        required: true
      - path: .env.local
        required: false
    image: electricsql/electric
    environment:
      DATABASE_URL: postgresql://${POSTGRES_USER}:${POSTGRES_PASSWORD}@postgres:5432/${ELECTRIC_DB}?sslmode=disable
      ELECTRIC_INSECURE: true
    ports:
      - "${ELECTRIC_PORT}:${ELECTRIC_PORT}"
    depends_on:
      - postgres