version: '3'

tasks:
  compose:up:
    desc: 'Run docker-compose up'
    cmds:
      - docker compose --file .base/docker-compose.yml up -d
  
  compose:down:
    desc: 'Run docker-compose down'
    cmds:
      - docker compose --file .base/docker-compose.yml down --remove-orphans

  drizzle:up:
    desc: 'Keep db schema up to date'
    cmds:
      - bun drizzle-kit --config ./base/backend/db/drizzle.config.ts push