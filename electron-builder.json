{"appId": "com.tinimelon.desktop", "productName": "<PERSON><PERSON>", "directories": {"output": "dist-electron"}, "files": ["electron/**/*", "dist/**/*", "node_modules/**/*", "package.json"], "extraResources": [{"from": "electron/icon.png", "to": "icon.png"}], "mac": {"category": "public.app-category.productivity", "icon": "electron/icon.png", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"icon": "electron/icon.png", "target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "publisherName": "<PERSON>i <PERSON> Team"}, "linux": {"icon": "electron/icon.png", "category": "Office", "target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}, {"target": "rpm", "arch": ["x64"]}]}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true, "shortcutName": "<PERSON><PERSON>"}, "dmg": {"title": "<PERSON>i <PERSON> ${version}", "icon": "electron/icon.png", "background": "build/dmg-background.png", "contents": [{"x": 410, "y": 150, "type": "link", "path": "/Applications"}, {"x": 130, "y": 150, "type": "file"}]}, "publish": {"provider": "github", "owner": "your-github-username", "repo": "tini-melon"}, "compression": "normal", "artifactName": "${productName}-${version}-${os}-${arch}.${ext}"}