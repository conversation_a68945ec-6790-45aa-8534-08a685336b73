# Architecture Overview

## Core Principles

The Scaf codebase follows a unique architecture built around the pumped-fn pattern, with these core principles:

1. **Dependency Injection**: All dependencies are explicitly declared and injected
2. **Reactive State**: State changes propagate automatically to dependent components
3. **Base/Mod Separation**: Clear separation between reusable infrastructure and application code
4. **Command-Based Structure**: Functionality is organized into executable commands
5. **Type Safety**: TypeScript and Zod schemas ensure type safety throughout the application

## Key Architectural Patterns

### Pumped-fn Pattern

The foundation of the architecture is the pumped-fn pattern, which uses `provide` and `derive` functions:

```typescript
// Providing a value
const config = provide(() => getConfig());

// Deriving a value from dependencies
const connection = derive(
  [config, logger('db')], 
  async ([config, logger], ctl) => {
    // Implementation using dependencies
  }
);
```

This pattern enables:
- Explicit dependency declaration
- Automatic dependency resolution
- Reactive updates when dependencies change
- Cleanup via the control object (`ctl`)

### Command Pattern

The application is structured around commands that can be executed from the command line:

```typescript
// base/cmds/server.ts
export default derive(
  [serverConfig, logger('server'), rpc, shape],
  async ([config, logger, rpc, shape], ctl) => {
    // Server implementation
  }
)
```

Commands are discovered and executed by the command runner in `base/cmd.ts`, which prioritizes mod commands over base commands.

### Base/Mod Extension

The codebase is divided into `base/` (reusable infrastructure) and `mod/` (application-specific code). Base components often import from mod to allow customization:

```typescript
// base/db/schema.ts
export * from '@/mod/backend/schema'
```

This creates extension points where mod can customize behavior without modifying base code.

### RPC Pattern

API communication uses a type-safe RPC system:

```typescript
// mod/dual/rpc.ts
export const userEndpoint = define.service({
  createUser: {
    input: schema.UserInsertSchema,
    output: z.void(),
  }
})

// mod/backend/routes.ts
export const createUserRoute = router.implements(
  'createUser',
  derive(
    [connection, logger('route.createUser')],
    async ([db, logger]) => async ({ input }) => {
      // Implementation
    }
  )
)
```

This ensures type safety and clear separation between interface and implementation.

### React Integration

React components use `@pumped-fn/react` to subscribe to state changes:

```typescript
// Using Resolves component
<Resolves e={[users.reactive, selectedUser.reactive, setSelectedUser]}>
  {([users, selectedUser, setSelectedUser]) => (
    // Component implementation using resolved values
  )}
</Resolves>

// Using useResolves hook
const [selectedUser, userTodos] = useResolves(
  app.selectedUser.reactive, 
  app.userTodos.reactive
);
```

This creates a reactive UI that automatically updates when state changes.

## System Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                        Frontend                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │   React     │    │  pumped-fn  │    │   Tailwind  │     │
│  │ Components  │◄──►│    State    │    │     CSS     │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                         Backend                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │    RPC      │    │  pumped-fn  │    │   Command   │     │
│  │   System    │◄──►│ Dependencies│◄──►│   Runner    │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
└───────────────────────────┬─────────────────────────────────┘
                            │
                            ▼
┌─────────────────────────────────────────────────────────────┐
│                        Database                             │
│  ┌─────────────┐    ┌─────────────┐    ┌─────────────┐     │
│  │  PostgreSQL │    │  Drizzle    │    │ ElectricSQL │     │
│  │  Database   │◄──►│     ORM     │◄──►│  Real-time  │     │
│  └─────────────┘    └─────────────┘    └─────────────┘     │
└─────────────────────────────────────────────────────────────┘
```

The architecture enables a seamless flow of data from the database to the UI and back, with automatic reactivity at each layer.

