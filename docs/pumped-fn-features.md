# Pumped-fn Features and Examples

This document provides a detailed look at pumped-fn features and how they're used in both frontend and backend contexts.

## Core Features

### 1. Dependency Tracking

Pumped-fn tracks dependencies between state containers and derived values, ensuring that functions only execute when their dependencies change.

```typescript
// This function only executes when users or searchTerm changes
const filteredUsers = derive(
  [users.reactive, searchTerm.reactive],
  ([users, searchTerm]) => users.filter(user => 
    user.name.includes(searchTerm)
  )
);
```

### 2. Selective Reactivity

Components and derived values only update when their specific reactive dependencies change, not on every state change.

```typescript
// This component only re-renders when selectedUser changes
const UserInfo = () => {
  const [selectedUser] = useResolves(app.selectedUser.reactive);
  return <div>{selectedUser?.name}</div>;
};
```

### 3. Unified State and Controllers

State and controllers are bundled together, providing a clean API for components.

```typescript
export const app = {
  users,
  todos,
  selectedUser,
  setSelectedUser,
  userTodos,
  todosCtl
};
```

### 4. Async Support

Pumped-fn supports async functions in derive, enabling integration with APIs and databases.

```typescript
const userDetails = derive(
  [selectedUserId.reactive, api],
  async ([userId, api]) => {
    if (!userId) return null;
    return await api.fetchUserDetails(userId);
  }
);
```

## Frontend Examples

### State Definition

```typescript
// mod/frontend/pumped.todo.ts
import { provide, derive } from "@pumped-fn/core-next";

// Create state
export const todos = provide(() => initialTodos);
export const filter = provide(() => 'all' as 'all' | 'active' | 'completed');

// Derived state
export const filteredTodos = derive(
  [todos.reactive, filter.reactive],
  ([todos, filter]) => {
    if (filter === 'active') return todos.filter(todo => !todo.completed);
    if (filter === 'completed') return todos.filter(todo => todo.completed);
    return todos;
  }
);

// Controller
export const todosCtl = derive(
  [todos.static],
  ([todosCtl]) => ({
    addTodo: (title: string) => {
      todosCtl.update(todos => [
        ...todos,
        { id: Date.now(), title, completed: false }
      ]);
    },
    toggleTodo: (id: number) => {
      todosCtl.update(todos => todos.map(todo => 
        todo.id === id ? { ...todo, completed: !todo.completed } : todo
      ));
    }
  })
);
```

### Component Integration

```typescript
// mod/frontend/components/TodoList.tsx
import { useResolves } from '@pumped-fn/react';

const TodoList = () => {
  const [filteredTodos, todosCtl, filter, setFilter] = useResolves(
    app.filteredTodos.reactive,
    app.todosCtl,
    app.filter.reactive,
    app.setFilter
  );
  
  return (
    <div>
      <div>
        <button onClick={() => setFilter('all')}>All</button>
        <button onClick={() => setFilter('active')}>Active</button>
        <button onClick={() => setFilter('completed')}>Completed</button>
      </div>
      
      <ul>
        {filteredTodos.map(todo => (
          <li key={todo.id}>
            <input
              type="checkbox"
              checked={todo.completed}
              onChange={() => todosCtl.toggleTodo(todo.id)}
            />
            <span>{todo.title}</span>
          </li>
        ))}
      </ul>
    </div>
  );
};
```

## Backend Examples

### Database Connection

```typescript
// base/db/index.ts
import { provide, derive } from "@pumped-fn/core-next";
import { drizzle } from 'drizzle-orm/postgres-js';
import postgres from 'postgres';

// Database configuration
export const dbConfig = provide(() => ({
  host: process.env.DB_HOST || 'localhost',
  port: Number(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'scaf',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
}));

// Database connection
export const db = derive(
  [dbConfig.reactive, logger('db')],
  async ([config, logger]) => {
    logger.info('Connecting to database', { host: config.host, database: config.database });
    
    const client = postgres({
      host: config.host,
      port: config.port,
      database: config.database,
      user: config.user,
      password: config.password,
    });
    
    return drizzle(client);
  }
);
```

### API Routes

```typescript
// mod/backend/routes.ts
import { derive } from "@pumped-fn/core-next";
import { db } from "@/base/db";
import { todos } from "./schema";
import { eq } from "drizzle-orm";

// Get todos route
export const getTodosRoute = derive(
  [db, logger('route.getTodos')],
  async ([db, logger]) => async ({ input }) => {
    const { userId } = input;
    logger.info('Getting todos for user', { userId });
    
    const result = await db.select().from(todos).where(eq(todos.userId, userId));
    return result;
  }
);

// Create todo route
export const createTodoRoute = derive(
  [db, logger('route.createTodo')],
  async ([db, logger]) => async ({ input }) => {
    const { userId, title, description } = input;
    logger.info('Creating todo for user', { userId, title });
    
    const result = await db.insert(todos).values({
      userId,
      title,
      description,
      completed: false,
      createdAt: new Date(),
      updatedAt: new Date(),
    }).returning();
    
    return result[0];
  }
);
```

## Integration Between Frontend and Backend

### API Client

```typescript
// mod/frontend/api.ts
import { provide, derive } from "@pumped-fn/core-next";

// API client
export const api = derive(
  [logger('api')],
  ([logger]) => ({
    getTodos: async (userId: number) => {
      logger.info('Fetching todos for user', { userId });
      const response = await fetch(`/api/todos?userId=${userId}`);
      return response.json();
    },
    
    createTodo: async (todo: TodoInsert) => {
      logger.info('Creating todo', { todo });
      const response = await fetch('/api/todos', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(todo),
      });
      return response.json();
    }
  })
);
```

### Frontend State with API Integration

```typescript
// mod/frontend/pumped.todo.ts
import { provide, derive } from "@pumped-fn/core-next";
import { api } from "./api";

// User state
export const selectedUserId = provide(() => null as number | null);

// Todos state with API integration
export const todos = provide(() => [] as Todo[]);
export const loadTodos = derive(
  [selectedUserId.reactive, api, todos.static],
  async ([userId, api, todosCtl]) => {
    if (!userId) return;
    
    const fetchedTodos = await api.getTodos(userId);
    todosCtl.update(fetchedTodos);
  }
);

// Controller with API integration
export const todosCtl = derive(
  [todos.static, selectedUserId.reactive, api],
  ([todosCtl, userId, api]) => ({
    addTodo: async (title: string, description?: string) => {
      if (!userId) return;
      
      const newTodo = await api.createTodo({
        userId,
        title,
        description,
        completed: false,
      });
      
      todosCtl.update(todos => [...todos, newTodo]);
    }
  })
);
```

## Advanced Features

### 1. Dependency Injection

Pumped-fn enables clean dependency injection throughout the application:

```typescript
// Inject logger into a component
const userService = derive(
  [db, logger('userService')],
  ([db, logger]) => ({
    getUser: async (id: number) => {
      logger.info('Getting user', { id });
      return await db.select().from(users).where(eq(users.id, id));
    }
  })
);
```

### 2. Lazy Initialization

State can be initialized lazily, only when it's actually needed:

```typescript
const expensiveResource = derive(
  [isNeeded.reactive],
  async ([isNeeded]) => {
    if (!isNeeded) return null;
    
    // Only initialize when needed
    return await loadExpensiveResource();
  }
);
```

### 3. Cleanup Functions

Derive functions can return cleanup functions for resource management:

```typescript
const websocketConnection = derive(
  [serverUrl.reactive],
  async ([url], ctl) => {
    if (!url) return null;
    
    const socket = new WebSocket(url);
    
    // Return cleanup function
    return () => {
      socket.close();
    };
  }
);
```

### 4. Error Handling

Pumped-fn provides mechanisms for handling errors in async functions:

```typescript
const userProfile = derive(
  [userId.reactive, api, errorState.static],
  async ([userId, api, errorCtl]) => {
    if (!userId) return null;
    
    try {
      return await api.getUserProfile(userId);
    } catch (error) {
      errorCtl.update({
        message: 'Failed to load user profile',
        error
      });
      return null;
    }
  }
);
```

## Links to Related Documentation

- [Pumped-fn Pattern](pumped-fn-pattern.md) - Core concepts of the pumped-fn pattern
- [State Definition](state-definition.md) - How state is defined using pumped-fn
- [Component Integration](component-integration.md) - How React components consume pumped-fn state
- [Data Flow](data-flow.md) - How data flows through the application

