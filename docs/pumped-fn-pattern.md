# Pumped-fn Pattern

The Scaf codebase uses the pumped-fn pattern for state management and dependency injection.

## Core Concepts

### provide and derive

```typescript
import { provide, derive } from "@pumped-fn/core-next";

// Create state
export const users = provide(() => initialUsers);

// Create derived state - executes ONLY when dependencies change
export const filteredUsers = derive(
  [users.reactive, searchTerm.reactive],
  ([users, searchTerm]) => users.filter(user => 
    user.name.includes(searchTerm)
  )
);

// Create controller
export const setSearchTerm = derive(
  [searchTerm.static],
  ([searchTermCtl]) => (newTerm) => searchTermCtl.update(newTerm)
);
```

### Reactive Execution Model

The key feature of pumped-fn is its reactive execution model:

1. **Reactive Dependencies** (`.reactive`): Trigger re-execution when they change
2. **Static Dependencies** (`.static`): Access controllers without triggering re-execution

Functions only execute when their reactive dependencies change.

### React Integration

```typescript
// Resolves component
const UserSelector = () => (
  <Resolves e={[users.reactive, selectedUser.reactive, setSelectedUser]}>
    {([users, selectedUser, setSelectedUser]) => (
      // Renders ONLY when users or selectedUser changes
      <select onChange={e => setSelectedUser(e.target.value)}>
        {users.map(user => <option value={user.id}>{user.name}</option>)}
      </select>
    )}
  </Resolves>
);

// useResolves hook
const TodoList = () => {
  const [selectedUser, userTodos] = useResolves(
    app.selectedUser.reactive,
    app.userTodos.reactive
  );
  
  // Component re-renders ONLY when selectedUser or userTodos changes
  return <div>{userTodos.map(todo => <div>{todo.title}</div>)}</div>;
};
```

### Dependency Chain

Changes propagate through the dependency chain:

```typescript
// When selectedUserId changes:
// 1. selectedUser recalculates
// 2. userTodos recalculates
// 3. Components using userTodos.reactive update

export const selectedUserId = provide(() => undefined);

export const selectedUser = derive(
  [users.reactive, selectedUserId.reactive],
  ([users, userId]) => users.find(user => user.id === userId)
);

export const userTodos = derive(
  [selectedUser.reactive, todos.reactive],
  ([user, todos]) => todos.filter(todo => todo.userId === user?.id)
);
```

### Unified Export Pattern

```typescript
export const app = {
  users,
  todos,
  selectedUser,
  setSelectedUser,
  userTodos,
  todosCtl
};
```

## Key Benefits

1. **Explicit Dependencies**: Dependencies are clearly declared
2. **Selective Reactivity**: Functions only execute when dependencies change
3. **Separation of Concerns**: State definition separate from consumption
4. **Type Safety**: TypeScript ensures type safety throughout

