# Component Integration

This document explains how React components integrate with the pumped-fn state management system.

## Integration Patterns

There are two main patterns for integrating pumped-fn state with React components:

1. **Resolves Component**: A component wrapper that resolves dependencies
2. **useResolves Hook**: A hook that resolves dependencies

Both patterns provide reactive updates **only when reactive dependencies change**.

## Resolves Component Pattern

```typescript
import { Resolves } from '@pumped-fn/react';

const UserSelector = () => (
  <Resolves e={[users.reactive, selectedUser.reactive, setSelectedUser]}>
    {([users, selectedUser, setSelectedUser]) => (
      // Renders ONLY when users or selectedUser changes
      <select 
        value={selectedUser?.id} 
        onChange={e => setSelectedUser(Number(e.target.value))}
      >
        {users.map(user => (
          <option key={user.id} value={user.id}>{user.name}</option>
        ))}
      </select>
    )}
  </Resolves>
);
```

Key points:
- The `e` prop takes an array of dependencies to resolve
- The children function receives the resolved values in the same order
- The component re-renders **only when reactive dependencies change**

## useResolves Hook Pattern

```typescript
import { useResolves } from '@pumped-fn/react';

const TodoList = () => {
  const [selectedUser, userTodos] = useResolves(
    app.selectedUser.reactive, 
    app.userTodos.reactive
  );
  
  // Component re-renders ONLY when selectedUser or userTodos changes
  return (
    <div>
      <h2>Tasks for {selectedUser?.name}</h2>
      <ul>
        {userTodos.map(todo => (
          <li key={todo.id}>{todo.title}</li>
        ))}
      </ul>
    </div>
  );
};
```

Key points:
- The hook takes any number of dependencies as arguments
- It returns an array of resolved values in the same order
- The component re-renders **only when reactive dependencies change**

## Reactive vs. Non-Reactive Dependencies

1. **Reactive Dependencies** (`.reactive`):
   - Trigger component re-renders when their value changes
   - Used for state that components need to display

2. **Non-Reactive Dependencies** (controller functions or `.static`):
   - Don't trigger re-renders when accessed
   - Used for actions that modify state

This distinction allows for efficient rendering - components only update when the data they display actually changes.

## Accessing Controllers

```typescript
const TodoItem = ({ todo }) => (
  <Resolves e={[todosCtl]}>
    {([todosCtl]) => (
      <div>
        <input
          type="checkbox"
          checked={todo.completed}
          onChange={() => todosCtl.toggleTodoStatus(todo.id)}
        />
        <span>{todo.title}</span>
        <button onClick={() => todosCtl.deleteTodo(todo.id)}>Delete</button>
      </div>
    )}
  </Resolves>
);
```

## Unified Import Pattern

```typescript
import { app } from '../pumped.todo';

const MyComponent = () => {
  const [selectedUser, userTodos, todosCtl] = useResolves(
    app.selectedUser.reactive,
    app.userTodos.reactive,
    app.todosCtl
  );
  
  // Component implementation
};
```

This makes it clear which domain the state belongs to and keeps imports organized.

