# Base/Mod Architecture

The Scaf codebase is organized into a unique base/mod architecture that separates reusable infrastructure from application-specific code.

## Directory Structure

```
/
├── base/           # Reusable infrastructure
│   ├── backend/    # Backend infrastructure
│   ├── cmds/       # Base commands
│   ├── db/         # Database infrastructure
│   ├── dual/       # Shared code (frontend/backend)
│   └── utils/      # Utility functions
├── mod/            # Application-specific code
│   ├── backend/    # Backend implementation
│   ├── cmds/       # Application commands
│   ├── dual/       # Shared types and RPC definitions
│   └── frontend/   # Frontend implementation
│       ├── components/  # React components
│       └── pumped.*.ts  # pumped-fn state definitions
```

## Core Principles

1. **Base**: Provides reusable infrastructure, utilities, and patterns
2. **Mod**: Contains application-specific implementations and customizations
3. **Extension over Modification**: Base components are designed to be extended rather than modified

## Extension Mechanisms

### 1. Import Redirection

Base components often import from mod to allow customization:

```typescript
// base/db/schema.ts
export * from '@/mod/backend/schema'
```

### 2. Command Resolution Order

The command system prioritizes mod commands over base commands:

```typescript
// base/cmd.ts
const cmd = await Promise.allSettled([
    import(`@/mod/cmds/${firstCmd}`),
    import(`@/base/cmds/${firstCmd}`)
  ])
  .then(results => results.find(r => r.status === "fulfilled")?.value)
```

### 3. Dependency Injection

The pumped-fn pattern enables mod components to use base infrastructure while providing their own implementations:

```typescript
// mod/backend/routes.ts
export const createUserRoute = derive(
  [connection, logger('route.createUser')],
  async ([db, logger]) => async ({ input }) => {
    // Implementation using base-provided dependencies
  }
)
```

## Frontend Organization

The frontend code in the mod directory is organized into:

- **components**: React components
- **pumped.*.ts**: State definitions using pumped-fn

## Backend Organization

The backend code is organized into:

- **routes.ts**: API route definitions
- **schema.ts**: Database schema definitions
- **shapes.ts**: Data shape definitions for ElectricSQL

## Dual Code

The dual code is shared between frontend and backend:

- **types.ts**: TypeScript type definitions
- **rpc.ts**: RPC endpoint definitions
- **logger.ts**: Logging utility

## Benefits of the Base/Mod Pattern

1. **Reusability**: Base components can be reused across multiple applications
2. **Maintainability**: Clear separation of concerns
3. **Flexibility**: Mod can override or extend base behavior
4. **Testability**: Components can be tested in isolation

