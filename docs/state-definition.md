# State Definition

In the Scaf codebase, state is defined in dedicated `pumped.*.ts` files using the pumped-fn pattern.

## State Definition Files

State is organized into domain-specific files:

- `pumped.todo.ts`: Todo application state
- `pumped.theme.ts`: Theme management state

## Basic State with provide

The `provide` function creates a state container with an initial value:

```typescript
import { provide } from "@pumped-fn/core-next";

// State with hardcoded initial value
export const users = provide(() => mockUsers);

// State with computed initial value
export const currentTheme = provide(() => {
  const storedTheme = localStorage.getItem('theme') as Theme | null;
  return storedTheme || 'light';
});

// State with undefined initial value
export const selectedUserId = provide(() => undefined as number | undefined);
```

## Computed State with derive

The `derive` function creates computed state that depends on other state:

```typescript
import { derive } from "@pumped-fn/core-next";

// Computed state based on multiple dependencies
export const selectedUser = derive(
  [users.reactive, selectedUserId.reactive], 
  ([users, userId]) => {
    if (userId === undefined) return null;
    return users.find(user => user.id === userId) || null;
  }
);

// Filtered state
export const userTodos = derive(
  [selectedUser.reactive, todos.reactive],
  ([user, todos]) => {
    if (!user) return [];
    return todos.filter(todo => todo.userId === user.id);
  }
);
```

## Controller Functions with derive

Controller functions are created with `derive` to update state:

```typescript
// Simple controller function
export const setSelectedUser = derive(
  [selectedUserId.static], 
  ([userIdCtl]) => (id: number) => userIdCtl.update(id)
);

// Complex controller with multiple operations
export const todosCtl = derive(
  [todos.static],
  ([todosCtl]) => ({
    addTodo: (todo) => {
      todosCtl.update(todos => [...todos, todo]);
    },
    toggleTodoStatus: (todoId) => {
      todosCtl.update(todos =>
        todos.map(todo =>
          todo.id === todoId
            ? { ...todo, completed: !todo.completed }
            : todo
        )
      );
    },
    deleteTodo: (todoId) => {
      todosCtl.update(todos => todos.filter(todo => todo.id !== todoId));
    }
  })
);
```

## Unified Export Pattern

Related state and controllers are bundled into a unified export object:

```typescript
// Bundle all todo-related state and controllers
export const app = {
  users,
  todos,
  selectedUser,
  setSelectedUser,
  userTodos,
  todosCtl
};
```

## State with Side Effects

State can include side effects, such as persisting to localStorage:

```typescript
export const switchTheme = derive([currentTheme.static], ([themeCtl]) => {
  return () => {
    const newTheme = themeCtl.get() === 'light' ? 'dark' : 'light';
    themeCtl.update(newTheme);
    localStorage.setItem('theme', newTheme);
  };
});
```

## Accessing State

State can be accessed in two ways:

1. **Reactive Access**: For subscribing to changes in UI components
   ```typescript
   // In a derive function
   export const selectedUser = derive([users.reactive, selectedUserId.reactive], ...);
   
   // In a React component
   const [users, selectedUser] = useResolves(app.users.reactive, app.selectedUser.reactive);
   ```

2. **Static Access**: For accessing the controller to update state
   ```typescript
   // In a derive function
   export const setSelectedUser = derive([selectedUserId.static], ...);
   
   // In a controller function
   const currentUser = selectedUser.get();
   userIdCtl.update(id);
   ```

This pattern provides a clean, organized way to define and manage state in the application.

