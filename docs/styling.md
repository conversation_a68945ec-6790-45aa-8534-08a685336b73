# Styling with <PERSON><PERSON><PERSON> and Daisy<PERSON>

The Scaf codebase uses Tailwind CSS and Daisy<PERSON> for styling components.

## Tailwind CSS Integration

Tailwind CSS is used for utility-based styling throughout the application:

```typescript
// Example of Tailwind utility classes
<div className="flex justify-between items-center mb-6">
  <h2 className="text-xl font-semibold flex items-center gap-2">
    Tasks
  </h2>
  
  <div className="space-y-4">
    {/* Content */}
  </div>
</div>
```

## DaisyUI Components

DaisyUI provides pre-styled components built on top of Tailwind:

```typescript
// Card component
<div className="card bg-base-100 shadow-sm hover:shadow-md transition-shadow duration-300">
  <div className="card-body p-4">
    <h2 className="card-title text-lg font-medium mb-2">Title</h2>
    {/* Card content */}
  </div>
</div>

// Form controls
<div className="form-control w-full">
  <select className="select select-bordered w-full focus:ring-2 focus:ring-primary/50">
    {/* Options */}
  </select>
</div>

// Buttons
<button className="btn btn-primary">Primary Button</button>
<button className="btn btn-circle btn-ghost">
  {/* Icon */}
</button>

// Tabs
<div className="tabs tabs-boxed">
  <button className="tab tab-active">Active Tab</button>
  <button className="tab">Inactive Tab</button>
</div>
```

## Theme Support

The application supports light and dark themes using DaisyUI's theming system:

```html
<!-- Set theme on the html element -->
<html data-theme="light">
  <!-- or -->
<html data-theme="dark">
```

Theme-related classes automatically adapt to the current theme:

```typescript
// These classes adapt to the current theme
<div className="bg-base-100 text-base-content">
  <p className="text-primary">Primary color text</p>
  <div className="bg-secondary text-secondary-content">
    Secondary background with contrasting text
  </div>
</div>
```

## Responsive Design

The application uses Tailwind's responsive prefixes for different screen sizes:

```typescript
// Responsive grid layout
<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
  <div className="lg:col-span-1">
    {/* Sidebar content */}
  </div>
  <div className="lg:col-span-2">
    {/* Main content */}
  </div>
</div>

// Responsive padding and margin
<main className="container mx-auto px-4 py-6 max-w-5xl">
  {/* Content */}
</main>
```

## Transitions and Animations

The application uses Tailwind's transition utilities for smooth interactions:

```typescript
// Hover effects
<div className="hover:shadow-md transition-shadow duration-300">
  {/* Content */}
</div>

// State transitions
<div className={`transition-all duration-300 ${isExpanded ? 'h-auto' : 'h-0'}`}>
  {/* Content */}
</div>
```

## Common UI Patterns

### Cards

```typescript
<div className="card bg-base-100 shadow-sm">
  <div className="card-body p-4">
    <h2 className="card-title">Card Title</h2>
    <p>Card content</p>
    <div className="card-actions justify-end">
      <button className="btn btn-primary">Action</button>
    </div>
  </div>
</div>
```

### Forms

```typescript
<form onSubmit={handleSubmit}>
  <div className="form-control mb-3">
    <label className="label">
      <span className="label-text">Input Label</span>
    </label>
    <input 
      type="text" 
      className="input input-bordered w-full" 
      placeholder="Enter text"
    />
  </div>
  
  <div className="form-control mb-3">
    <label className="label">
      <span className="label-text">Select Label</span>
    </label>
    <select className="select select-bordered w-full">
      <option disabled selected>Select an option</option>
      <option>Option 1</option>
      <option>Option 2</option>
    </select>
  </div>
  
  <button type="submit" className="btn btn-primary w-full">
    Submit
  </button>
</form>
```

### Lists

```typescript
<div className="space-y-4">
  {items.map(item => (
    <div key={item.id} className="card bg-base-100 shadow-sm">
      <div className="card-body p-4">
        <h3 className="font-medium">{item.title}</h3>
        <p className="text-sm text-base-content/70">{item.description}</p>
      </div>
    </div>
  ))}
</div>
```

These styling patterns provide a consistent, responsive UI throughout the application.

