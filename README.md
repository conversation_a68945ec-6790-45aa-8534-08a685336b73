# Scaf Repository

## Overview

Scaf is a Bun-React application with a modular architecture, using the pumped-fn pattern for state management and dependency injection.

## Documentation

The documentation for this repository is located in the [.instructions](.instructions) directory. It provides detailed information about the repository's structure, architecture, and patterns.

### Key Documents

1. [**Overview**](.instructions/index.md) - Start here for a comprehensive overview of the repository
2. [**Pumped-fn Pattern**](.instructions/pumped-fn-pattern.md) - Core pattern for state management and dependency injection
3. [**State Definition**](.instructions/state-definition.md) - How state is defined using pumped-fn
4. [**Component Integration**](.instructions/component-integration.md) - How React components consume pumped-fn state
5. [**Base/Mod Architecture**](.instructions/base-mod-architecture.md) - How the codebase is organized into base and mod directories
6. [**Data Flow**](.instructions/data-flow.md) - How data flows through the application
7. [**Styling**](.instructions/styling.md) - Tailwind and DaisyUI usage

## Key Concepts

### Base-Mod Pattern

The repository follows a modular architecture with a clear separation between reusable infrastructure (`base`) and application-specific customizations (`mod`).

### Pumped-fn Pattern

The repository uses pumped-fn's `provide`/`derive` pattern for state management and dependency injection, enabling reactive UI updates and clean separation of concerns.

### Reactive Components

React components use `@pumped-fn/react` to subscribe to state changes, with components only re-rendering when their specific reactive dependencies change.

## Technology Stack

- **Runtime**: Bun
- **Frontend**: React, Tailwind CSS, DaisyUI
- **State Management**: pumped-fn
- **Backend**: Bun server
- **Database**: PostgreSQL with Drizzle ORM

