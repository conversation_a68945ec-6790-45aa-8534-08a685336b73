{"compilerOptions": {"target": "ESNext", "module": "ESNext", "strict": true, "moduleResolution": "bundler", "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "outDir": "./dist", "jsx": "react-jsx", "rootDir": "./", "paths": {"@/mod/*": ["./mod/*"], "@/base/*": ["./base/*"]}}, "include": ["**/*"], "exclude": ["node_modules", "dist", "build", "coverage", "scripts"]}