const { contextBridge, ipcRenderer } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // App information
  getVersion: () => ipcRenderer.invoke('app-version'),
  
  // Dialog methods
  showMessageBox: (options) => ipcRenderer.invoke('show-message-box', options),
  showOpenDialog: (options) => ipcRenderer.invoke('show-open-dialog', options),
  showSaveDialog: (options) => ipcRenderer.invoke('show-save-dialog', options),
  
  // Menu event listeners
  onMenuNewTodo: (callback) => {
    const subscription = (event, ...args) => callback(...args);
    ipcRenderer.on('menu-new-todo', subscription);
    
    // Return unsubscribe function
    return () => {
      ipcRenderer.removeListener('menu-new-todo', subscription);
    };
  },
  
  // Theme management
  onThemeChange: (callback) => {
    const subscription = (event, theme) => callback(theme);
    ipcRenderer.on('theme-changed', subscription);
    
    return () => {
      ipcRenderer.removeListener('theme-changed', subscription);
    };
  },
  
  // Window controls
  minimizeWindow: () => ipcRenderer.invoke('minimize-window'),
  maximizeWindow: () => ipcRenderer.invoke('maximize-window'),
  closeWindow: () => ipcRenderer.invoke('close-window'),
  
  // File operations
  readFile: (filePath) => ipcRenderer.invoke('read-file', filePath),
  writeFile: (filePath, content) => ipcRenderer.invoke('write-file', filePath, content),
  
  // System information
  platform: process.platform,
  
  // Development helpers
  isDev: process.env.NODE_ENV === 'development',
  
  // Notification support
  showNotification: (title, body, options = {}) => {
    if ('Notification' in window && Notification.permission === 'granted') {
      return new Notification(title, { body, ...options });
    }
    return null;
  },
  
  requestNotificationPermission: async () => {
    if ('Notification' in window) {
      return await Notification.requestPermission();
    }
    return 'denied';
  }
});

// Expose a limited set of Node.js APIs for development
if (process.env.NODE_ENV === 'development') {
  contextBridge.exposeInMainWorld('devAPI', {
    // Development-only APIs
    openDevTools: () => ipcRenderer.invoke('open-dev-tools'),
    reloadApp: () => ipcRenderer.invoke('reload-app')
  });
}

// Security: Remove any global Node.js APIs that might have been exposed
delete window.require;
delete window.exports;
delete window.module;

// Log that preload script has loaded (development only)
if (process.env.NODE_ENV === 'development') {
  console.log('Preload script loaded successfully');
}
