import type * as s from "@/mod/backend/schema"
import type { InferSelectModel, InferInsertModel, Table } from "drizzle-orm";

export type selects = {
  [K in keyof typeof s as typeof s[K] extends { $inferSelect: any } ? K : never]: typeof s[K] extends Table
    ? InferSelectModel<typeof s[K]>
    : never;
}

export type inserts = {
  [K in keyof typeof s as typeof s[K] extends { $inferInsert: any } ? K : never]: typeof s[K] extends Table
    ? InferInsertModel<typeof s[K]>
    : never;
}