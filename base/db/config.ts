import { z } from "zod/v4-mini"
import "dotenv/config"

const dbConfig = z.object({
  host: z.string().check(
    z.minLength(1, "Database host is required")
  ),
  port: z.pipe(z.string(), z.transform(Number))
    .check(
      z.minimum(1, "Database port must be a positive number"),
      z.maximum(65535, "Database port must be less than 65536"
    )),
  user: z.string().check(z.minLength(1, "Database user is required")),
  password: z.optional(z.string()),
  database: z.string().check(z.minLength(1, "Database name is required")),
})

export const getDbConfig = (args: Dict<string> = process.env) => {
  return dbConfig.parse({
    host: args.POSTGRES_HOST || "localhost",
    port: args.POSTGRES_PORT,
    user: args.POSTGRES_USER,
    password: args.POSTGRES_PASSWORD,
    database: args.POSTGRES_DB,
  })
}