import { provide, derive } from "@pumped-fn/core-next";
import { getDbConfig } from "@/base/backend/config";
import { logger } from "@/base/dual/logger";
import { drizzle } from "drizzle-orm/node-postgres"

const config = provide(() => getDbConfig());
export const connection = derive([config, logger('db')], async ([config, logger], ctl) => {
  logger.info(`Connecting to database at ${config.host}:${config.port}...`)
  const db =  drizzle({
    connection: {
      connectionString: `postgres://${config.user}:${config.password}@${config.host}:${config.port}/${config.database}`,
      ssl: false
    },
    logger: {
      logQuery(query, params) {
        logger.debug(`Executing query: ${query} with params: ${JSON.stringify(params)}`);
      },
    }
  })

  logger.debug('testing connection to db')
  await db.execute("SELECT 1")
  logger.debug('db connection established successfully')
  return db
})

export * as schema from "@/base/db/schema";