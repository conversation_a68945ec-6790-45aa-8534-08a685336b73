import { z } from "zod/v4-mini"
import "dotenv/config"

const serverConfig = z.object({
  port: z.pipe(z.string(), z.transform(Number))
    .check(
      z.minimum(1, "Server port must be a positive number"),
      z.maximum(65535, "Server port must be less than 65536"
    )),
  host: z.optional(z.string().check(z.minLength(1, "Server host is required"))),
})

const dbConfig = z.object({
  host: z.string().check(
    z.minLength(1, "Database host is required")
  ),
  port: z.pipe(z.string(), z.transform(Number))
    .check(
      z.minimum(1, "Database port must be a positive number"),
      z.maximum(65535, "Database port must be less than 65536"
    )),
  user: z.string().check(z.minLength(1, "Database user is required")),
  password: z.optional(z.string()),
  database: z.string().check(z.minLength(1, "Database name is required")),
})

const electricConfig = z.object({
  host: z.string().check(
    z.minLength(1, "Electric host is required")
  ),
  port: z.pipe(z.string(), z.transform(Number))
    .check(
      z.minimum(1, "Electric port must be a positive number"),
      z.maximum(65535, "Electric port must be less than 65536"
    )),
})

export const getDbConfig = (args: Dict<string> = process.env) => {
  return dbConfig.parse({
    host: args.POSTGRES_HOST || "localhost",
    port: args.POSTGRES_PORT,
    user: args.POSTGRES_USER,
    password: args.POSTGRES_PASSWORD,
    database: args.POSTGRES_DB,
  })
}

export const getElectricConfig = (args: Dict<string> = process.env) => {
  return electricConfig.parse({
    host: args.ELECTRIC_HOST,
    port: args.ELECTRIC_PORT,
    secret: args.ELECTRIC_SECRET,
  })
}

export const getServerConfig = (args: Dict<string> = process.env) => {
  return serverConfig.parse({
    port: args.SERVER_PORT || "3000",
    host: args.SERVER_HOST || "0.0.0.0",
  })
}