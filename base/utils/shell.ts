import { Core, createScope } from "@pumped-fn/core-next"
import { logger, Logger } from "@/base/dual/logger"
import { signalHand<PERSON> } from "./handling-signals"

type ShellCallback = (scope: Core.Scope) => Promise<void> | void

export async function shell(cb: ShellCallback) {
  const scope = createScope()

  let instanceLogger: Logger = {
    info: console.log,
    warn: console.log,
    error: console.log,
    debug: console.log
  }
  
  try {
    instanceLogger = await scope.resolve(logger('shell'))

    signalHandler(instanceLogger)

    await cb(scope)
  } catch (error) {
    instanceLogger.error("Error during shell execution:", error)
    process.exit(1)
  } finally {
    await scope.dispose()
    process.exit(0)
  }
}

export async function run(executor: Core.Executor<void>) {
  await shell(scope => scope.resolve(executor))
}