type SignalType = 'SIGTERM' | 'SIGINT' | 'SIGHUP';
type SignalHandler = () => Promise<void> | void;
type ErrorHandler = (err: unknown) => Promise<void> | void;
type ShutdownHandler = () => Promise<void> | void;

interface LoggerLike {
  info: (...args: any[]) => void;
  warn: (...args: any[]) => void;
  error: (...args: any[]) => void;
  [key: string]: any;
}

class ProcessSignalHandler {
  private handlers: Map<SignalType, Set<SignalHandler>> = new Map();
  private errorHandlers: Set<ErrorHandler> = new Set();
  private shutdownHandlers: Set<ShutdownHandler> = new Set();
  private isShuttingDown = false;
  private static instance: ProcessSignalHandler;
  private logger: LoggerLike;

  private constructor(logger?: LoggerLike) {
    this.logger = logger || console;
    this.setupSignalHandlers();
    this.setupErrorHandlers();
  }

  static getInstance(logger?: LoggerLike): ProcessSignalHandler {
    if (!ProcessSignalHandler.instance) {
      ProcessSignalHandler.instance = new ProcessSignalHandler(logger);
    }
    return ProcessSignalHandler.instance;
  }

  private setupSignalHandlers(): void {
    (['SIGTERM', 'SIGINT', 'SIGHUP'] as SignalType[]).forEach((signal) => {
      process.on(signal, async () => {
        if (this.isShuttingDown) return;
        this.isShuttingDown = true;

        this.logger.info(`\nReceived ${signal}, starting graceful shutdown...`);

        try {
          const handlers = this.handlers.get(signal) || new Set();
          await Promise.allSettled(Array.from(handlers).map((handler) => handler()));
          await Promise.allSettled(Array.from(this.shutdownHandlers).map((fn) => fn()));
          process.exit(0);
        } catch (error) {
          this.logger.error('Error during shutdown:', error);
          await Promise.allSettled(Array.from(this.errorHandlers).map((fn) => fn(error)));
          process.exit(1);
        }
      });
    });
  }

  private setupErrorHandlers(): void {
    process.on('uncaughtException', async (err) => {
      this.logger.error('Uncaught Exception:', err);
      await Promise.allSettled(Array.from(this.errorHandlers).map((fn) => fn(err)));
      if (!this.isShuttingDown) {
        this.isShuttingDown = true;
        await Promise.allSettled(Array.from(this.shutdownHandlers).map((fn) => fn()));
        process.exit(1);
      }
    });
    process.on('unhandledRejection', async (reason) => {
      this.logger.error('Unhandled Rejection:', reason);
      await Promise.allSettled(Array.from(this.errorHandlers).map((fn) => fn(reason)));
      if (!this.isShuttingDown) {
        this.isShuttingDown = true;
        await Promise.allSettled(Array.from(this.shutdownHandlers).map((fn) => fn()));
        process.exit(1);
      }
    });
  }

  public onSignal(signal: SignalType, handler: SignalHandler): () => void {
    if (!this.handlers.has(signal)) {
      this.handlers.set(signal, new Set());
    }
    const handlers = this.handlers.get(signal)!;
    handlers.add(handler);

    // Return cleanup function
    return () => {
      handlers.delete(handler);
      if (handlers.size === 0) {
        this.handlers.delete(signal);
      }
    };
  }

  public onError(handler: ErrorHandler): () => void {
    this.errorHandlers.add(handler);
    return () => {
      this.errorHandlers.delete(handler);
    };
  }

  public onShutdown(handler: ShutdownHandler): () => void {
    this.shutdownHandlers.add(handler);
    return () => {
      this.shutdownHandlers.delete(handler);
    };
  }

  public setLogger(logger: LoggerLike) {
    this.logger = logger;
  }
}

export const signalHandler = ProcessSignalHandler.getInstance;