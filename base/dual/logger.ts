import { createConsola, ConsolaOptions } from "consola";
import { provide, Core, derive } from "@pumped-fn/core-next"

export type Logger = {
  info: (message: unknown, ...args: any[]) => void;
  warn: (message: unknown, ...args: any[]) => void;
  error: (message: unknown, ...args: any[]) => void;
  debug: (message: unknown, ...args: any[]) => void;
}

const defaultOptions = provide(() => ({
  formatOptions: {
    colors: true,
    compact: true
  },
}) satisfies Partial<ConsolaOptions>) 

const map = new Map<string, Core.Executor<Logger>>()

export const logger = (name: string, instantOpt?: Partial<ConsolaOptions>) => {
  if (map.has(name)) {
    return map.get(name)!
  }

  const cachedLogger = derive(defaultOptions, (options): Logger => {
    const instance = createConsola({
      ...options,
      ...instantOpt,
    })


    return instance.withTag(name)
  })

  map.set(name, cachedLogger)
  return cachedLogger
}