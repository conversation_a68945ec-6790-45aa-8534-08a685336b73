import { parseArgs } from "node:util";
import { Core, isMainExecutor } from "@pumped-fn/core-next";
import { run } from "./utils/shell";

const positionals = parseArgs({
  args: process.argv.slice(2),
  allowPositionals: true
})

const firstCmd = positionals.positionals[0]
const secondCmd = positionals.positionals.at(1)

if (!firstCmd) {
  console.error("No command provided. Please specify a command to run.");
  process.exit(1);
}

try {
  const cmd = await Promise.allSettled([
      import(`@/mod/cmds/${firstCmd}`),
      import(`@/base/cmds/${firstCmd}`)
    ])
    .then(results => results.find(r => r.status === "fulfilled")?.value)

  if (!cmd) {
    console.error(`Command "${firstCmd}" not found in both mod and base commands.`);
    process.exit(1);
  }

  if (!cmd.default) {
    console.error(`Command "${firstCmd}" does not have a default export.`);
    process.exit(1);
  }

  let mainExecutor: Core.Executor<void>

  if (secondCmd) {
    mainExecutor = cmd[secondCmd];
  } else {
    mainExecutor = cmd.default;
  }

  if (!mainExecutor) {
    console.error(`Command "${firstCmd}" does not export an executor function for "${secondCmd || 'default'}".`);
    process.exit(1);
  }

  if (!isMainExecutor(mainExecutor)) {
    console.error(`The executor for "${firstCmd}" is not a executor. Please ensure it is defined correctly.`);
    process.exit(1);
  }

  await run(mainExecutor as any)
} catch(error) {
  console.error(`Command "${firstCmd}" not found. Please check the command name and try again.`);
  process.exit(1);
}