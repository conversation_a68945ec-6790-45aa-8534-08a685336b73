# Tini Melon Desktop Application

This document provides instructions for running and building the Tini Melon desktop application using Electron.

## Quick Start

### Prerequisites

- Node.js 18+ or Bun runtime
- Git
- Platform-specific build tools (for distribution builds)

### Development

1. **Install Dependencies**
   ```bash
   bun install
   ```

2. **Start Development Environment**
   ```bash
   npm run electron:dev
   ```
   
   This will:
   - Start the Bun backend server
   - Launch Electron with hot reloading
   - Open DevTools for debugging

### Building

1. **Build Frontend Assets**
   ```bash
   npm run build:frontend
   ```

2. **Package Electron Application**
   ```bash
   npm run electron:build
   ```

3. **Create Distribution Packages**
   ```bash
   npm run electron:dist
   ```

## Available Scripts

| Script | Description |
|--------|-------------|
| `npm run electron:dev` | Start development environment |
| `npm run build:frontend` | Build frontend assets for production |
| `npm run electron:build` | Package Electron application |
| `npm run electron:pack` | Package without creating installers |
| `npm run electron:dist` | Create platform-specific installers |
| `npm run build:all` | Build frontend and package Electron app |

## Project Structure

```
tini-melon/
├── electron/                  # Electron main process files
│   ├── main.js               # Main Electron process
│   ├── preload.js            # Secure IPC bridge
│   ├── package.json          # Electron package config
│   └── icon.png              # Application icon
├── scripts/                  # Build and development scripts
│   ├── build-frontend.js     # Frontend bundling script
│   ├── build-electron.js     # Electron packaging script
│   └── dev-electron.js       # Development environment script
├── dist/                     # Built application files
│   ├── frontend/             # Bundled React application
│   └── backend/              # Backend server files
├── dist-electron/            # Packaged Electron applications
├── build/                    # Build configuration files
└── electron-builder.json     # Distribution configuration
```

## Features

### Desktop Integration

- **Native Menus**: Platform-specific application menus
- **Window Management**: Minimize, maximize, close, resize
- **Keyboard Shortcuts**: Standard desktop shortcuts
- **System Tray**: Background operation (planned)
- **Auto-updater**: Automatic updates (planned)

### Security

- **Context Isolation**: Secure renderer process
- **CSP Headers**: Content Security Policy protection
- **IPC Security**: Secure inter-process communication
- **External Link Handling**: Safe opening of external URLs

### Cross-Platform Support

- **Windows**: NSIS installer, portable executable
- **macOS**: DMG disk image, ZIP archive
- **Linux**: AppImage, DEB, RPM packages

## Development Workflow

### Hot Reloading

The development environment supports hot reloading for frontend changes. Backend changes require restarting the development server.

### Debugging

- **Renderer Process**: Use Chrome DevTools (automatically opened in dev mode)
- **Main Process**: Use `console.log()` statements (output appears in terminal)
- **IPC Communication**: Monitor IPC messages in DevTools

### Adding Features

1. **Frontend Changes**: Modify files in `mod/frontend/`
2. **Backend Changes**: Modify files in `mod/backend/` or `base/`
3. **Electron Features**: Modify `electron/main.js` or `electron/preload.js`
4. **Build Configuration**: Update `electron-builder.json`

## Troubleshooting

### Common Issues

1. **Application Won't Start**
   - Check that all dependencies are installed
   - Verify Node.js/Bun version compatibility
   - Check console output for error messages

2. **Build Failures**
   - Ensure frontend builds successfully first
   - Check platform-specific build requirements
   - Verify electron-builder configuration

3. **Database Connection Errors**
   - Start the database server (see main README)
   - Check database configuration in environment variables
   - Verify network connectivity

4. **IPC Communication Issues**
   - Check preload script is loading correctly
   - Verify contextBridge API exposure
   - Check for security policy violations

### Debug Commands

```bash
# Check Electron version
npx electron --version

# Validate electron-builder config
npx electron-builder --help

# Build with verbose output
DEBUG=electron-builder npm run electron:dist

# Check built application
npx electron dist-electron/linux-unpacked/
```

## Platform-Specific Notes

### Windows

- Requires Windows 10 or later
- Code signing certificate needed for distribution
- Windows Defender may flag unsigned builds

### macOS

- Requires macOS 10.15 or later
- Developer ID certificate needed for distribution
- Notarization required for Gatekeeper approval

### Linux

- Tested on Ubuntu 20.04+, Debian 11+
- AppImage provides universal compatibility
- Desktop integration varies by distribution

## Performance Tips

1. **Bundle Size**: Monitor bundle size with `npm run build:frontend`
2. **Memory Usage**: Use Chrome DevTools Memory tab
3. **Startup Time**: Minimize initial load operations
4. **Asset Optimization**: Compress images and resources

## Contributing

When contributing to the Electron implementation:

1. Follow existing code style and patterns
2. Test on multiple platforms when possible
3. Update documentation for new features
4. Consider security implications of changes
5. Maintain compatibility with existing architecture

## Support

For issues specific to the Electron implementation:

1. Check the troubleshooting section above
2. Review the comprehensive documentation in `docs/features/ELECTRON_DESKTOP_PACKAGING.md`
3. Check existing issues in the project repository
4. Create a new issue with detailed reproduction steps

## License

This project is licensed under the MIT License - see the LICENSE file for details.
