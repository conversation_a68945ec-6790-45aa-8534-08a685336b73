import { UserManagement } from '../dual/user-management.types.zod';

export const mockUsers: UserManagement[] = [
  {
    id: 1,
    firstName: '<PERSON>',
    lastName: 'Doe',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-15T10:30:00Z'),
    updatedAt: new Date('2024-01-20T14:45:00Z'),
  },
  {
    id: 2,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-16T09:15:00Z'),
    updatedAt: new Date('2024-01-18T11:20:00Z'),
  },
  {
    id: 3,
    firstName: '<PERSON>',
    lastName: '<PERSON>',
    email: '<EMAIL>',
    role: 'moderator',
    status: 'active',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-17T13:45:00Z'),
    updatedAt: new Date('2024-01-19T16:30:00Z'),
  },
  {
    id: 4,
    firstName: 'Sarah',
    lastName: 'Wilson',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-18T08:20:00Z'),
    updatedAt: new Date('2024-01-21T10:15:00Z'),
  },
  {
    id: 5,
    firstName: 'David',
    lastName: 'Brown',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-19T15:10:00Z'),
    updatedAt: new Date('2024-01-22T12:40:00Z'),
  },
  {
    id: 6,
    firstName: 'Emily',
    lastName: 'Davis',
    email: '<EMAIL>',
    role: 'moderator',
    status: 'active',
    createdAt: new Date('2024-01-20T11:25:00Z'),
    updatedAt: new Date('2024-01-23T09:50:00Z'),
  },
  {
    id: 7,
    firstName: 'Robert',
    lastName: 'Miller',
    email: '<EMAIL>',
    role: 'user',
    status: 'inactive',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-21T14:35:00Z'),
    updatedAt: new Date('2024-01-24T16:20:00Z'),
  },
  {
    id: 8,
    firstName: 'Lisa',
    lastName: 'Anderson',
    email: '<EMAIL>',
    role: 'admin',
    status: 'active',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-22T10:45:00Z'),
    updatedAt: new Date('2024-01-25T13:15:00Z'),
  },
  {
    id: 9,
    firstName: 'Chris',
    lastName: 'Taylor',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    createdAt: new Date('2024-01-23T12:20:00Z'),
    updatedAt: new Date('2024-01-26T15:30:00Z'),
  },
  {
    id: 10,
    firstName: 'Amanda',
    lastName: 'White',
    email: '<EMAIL>',
    role: 'user',
    status: 'active',
    avatar: 'https://img.daisyui.com/images/profile/demo/<EMAIL>',
    createdAt: new Date('2024-01-24T09:30:00Z'),
    updatedAt: new Date('2024-01-27T11:45:00Z'),
  },
];
