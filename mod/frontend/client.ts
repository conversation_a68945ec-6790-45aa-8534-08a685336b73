import { provide, derive } from "@pumped-fn/core-next"
import { client } from "@pumped-fn/extra"
import { endpoint } from "@/mod/dual/rpc"
import { logger } from "@/base/dual/logger"

export const sender = provide(() => fetch)

export const caller = client.createCaller(
  endpoint,
  derive(
    [sender, logger('caller')],
    ([fetcher, logger]) => {
      return async (def, path, param) => {
        logger.info(`Calling ${path} with params:`, param)

        return await fetcher(`/rpc`, {
          method: 'POST',
          body: JSON.stringify(param),
          headers: {
            'Content-Type': 'application/json',
            'X-Subject': path,
          },
        })
          .then(
            res => {
              if (!res.ok) {
                logger.error(`HTTP error! status: ${res.status}`)
                throw new Error(`HTTP error! status: ${res.status}`)
              }

              if (res.body) {
                logger.debug(`Response body received for ${path}`)
                return res.json()
              }

              logger.debug(`No response body for ${path}`)
              return undefined
            },
            error => {
              logger.error(`Fetch error for ${path}:`, error)
              throw error
            }
          )
      }
    }
  )
)