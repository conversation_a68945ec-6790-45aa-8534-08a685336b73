import { provide, derive } from "@pumped-fn/core-next";

type Theme = 'light' | 'dark';

export const currentTheme = provide(() => {
  const storedTheme = window.localStorage.getItem('theme') as Theme | null;
  if (storedTheme) {
    return storedTheme;
  }

  const userPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
  return userPrefersDark ? 'dark' : 'light';
})

export const switchTheme = derive([currentTheme.static], ([currentThemeCtl]) => {
  return () => {
    const newTheme = currentThemeCtl.get() === 'light' ? 'dark' : 'light';
    currentThemeCtl.update(newTheme);
    window.localStorage.setItem('theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };
});

export const theme = {
  currentTheme,
  switchTheme,
}