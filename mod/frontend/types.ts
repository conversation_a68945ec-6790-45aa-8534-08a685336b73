export interface User {
  id: number;
  email: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Todo {
  id: number;
  userId: number;
  title: string;
  description?: string;
  completed: boolean;
  status: 'todo' | 'in-progress' | 'done'; // Added status field for Kanban columns
  createdAt: Date;
  updatedAt: Date;
}

// Added new type for Kanban columns
export type KanbanColumn = {
  id: 'todo' | 'in-progress' | 'done';
  title: string;
  todos: Todo[];
}

