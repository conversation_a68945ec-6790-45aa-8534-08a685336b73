import { provide, derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";

import { mockUsers, mockTodos } from "./mockData"
import { TodoInsert, TodoInsertSchema } from "../dual/index.types.zod";
import { KanbanColumn } from "./types";

export const users = provide(() => mockUsers)
export const todos = provide(() => mockTodos)

export const selectedUserId = provide(() => undefined as number | undefined)
export const selectedUser = derive([users.reactive, selectedUserId.reactive], ([users, userId]) => {
  if (userId === undefined) return null;
  return users.find(user => user.id === userId) || null;
})

export const setSelectedUser = derive([selectedUserId.static], ([userIdCtl]) => {
  return (id: number) => {
    userIdCtl.update(id)
  }
})

export const userTodos = derive(
  [selectedUser.reactive, todos.reactive],
  ([user, todos]) => {
    if (!user) return [];
    return todos.filter(todo => todo.userId === user.id);
  }
)

export const kanbanColumns = derive(
  [userTodos.reactive],
  ([todos]) => {
    const columns: KanbanColumn[] = [
      { id: 'todo', title: 'To Do', todos: [] },
      { id: 'in-progress', title: 'In Progress', todos: [] },
      { id: 'done', title: 'Done', todos: [] }
    ];

    // Distribute todos into columns based on status
    todos.forEach(todo => {
      const column = columns.find(col => col.id === todo.status);
      if (column) {
        column.todos.push(todo);
      } else {
        // Fallback for todos without status
        if (todo.completed) {
          columns.find(col => col.id === 'done')?.todos.push(todo);
        } else {
          columns.find(col => col.id === 'todo')?.todos.push(todo);
        }
      }
    });

    return columns;
  }
)

export const todosCtl = derive(
  [todos.static, selectedUser.static, logger('todosCtl')],
  ([todosCtl, selectedUser, logger]) => ({
    addTodo: (todoData: TodoInsert) => {
      const currentUser = selectedUser.get();
      if (!currentUser) {
        logger.warn("No user selected, cannot add todo");
        return;
      }

      logger.info("Adding todo for user:", currentUser.name, "with data:", todoData);
      todosCtl.update(todos => [
        ...todos,
        {
          ...TodoInsertSchema.parse({
            ...todoData,
            userId: currentUser.id,
            completed: false,
          }),
          id: Math.max(0, ...todos.map(t => t.id)) + 1,
          status: 'todo', // Default status for new todos
          createdAt: new Date(),
          updatedAt: new Date()
        } as any
      ]);
    },
    toggleTodoStatus: (todoId: number) => {
      logger.info("Toggling todo status for ID:", todoId);
      todosCtl.update(todos =>
        todos.map(todo => {
          if (todo.id === todoId) {
            const completed = !todo.completed;
            // Update status based on completed state
            const status = completed ? 'done' : (todo.status === 'done' ? 'in-progress' : todo.status);
            return { ...todo, completed, status, updatedAt: new Date() };
          }
          return todo;
        })
      );
    },
    deleteTodo: (todoId: number) => {
      logger.info("Deleting todo with ID:", todoId);
      todosCtl.update(todos => todos.filter(todo => todo.id !== todoId));
    },
    moveTodo: (todoId: number, newStatus: 'todo' | 'in-progress' | 'done') => {
      logger.info(`Moving todo ${todoId} to ${newStatus}`);
      todosCtl.update(todos =>
        todos.map(todo => {
          if (todo.id === todoId) {
            // Update completed status based on the new column
            const completed = newStatus === 'done';
            return { ...todo, status: newStatus, completed, updatedAt: new Date() };
          }
          return todo;
        })
      );
    },
    reorderTodo: (sourceIndex: number, destinationIndex: number, sourceColumnId: string, destinationColumnId: string) => {
      logger.info(`Reordering todo from ${sourceColumnId}[${sourceIndex}] to ${destinationColumnId}[${destinationIndex}]`);
      
      // If moving between columns, use moveTodo
      if (sourceColumnId !== destinationColumnId) {
        const todos = todosCtl.get();
        const userTodosList = todos.filter(todo => todo.userId === selectedUser.get()?.id);
        const sourceColumn = userTodosList.filter(todo => todo.status === sourceColumnId);
        
        if (sourceColumn[sourceIndex]) {
          const todoId = sourceColumn[sourceIndex].id;
          todosCtl.update(todos =>
            todos.map(todo => {
              if (todo.id === todoId) {
                const completed = destinationColumnId === 'done';
                return { 
                  ...todo, 
                  status: destinationColumnId as 'todo' | 'in-progress' | 'done', 
                  completed, 
                  updatedAt: new Date() 
                };
              }
              return todo;
            })
          );
        }
      }
      // Reordering within the same column would be handled by the UI
    }
  })
)

export const app = {
  users,
  todos,
  selectedUser,
  setSelectedUser,
  userTodos,
  kanbanColumns,
  todosCtl
}
