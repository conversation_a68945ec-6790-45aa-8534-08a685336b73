import { Suspense, useState } from 'react';
import TodoApp from './components/TodoApp';
import UserManagementApp from './components/user-management/UserManagementApp';
import { ScopeProvider } from "@pumped-fn/react"

type AppView = 'todos' | 'users';

function App() {
  const [currentView, setCurrentView] = useState<AppView>('todos');

  return (
    <ScopeProvider>
      <Suspense>
        <div className="min-h-screen bg-base-200">
          {/* Navigation */}
          <div className="navbar bg-base-100 shadow-sm">
            <div className="navbar-start">
              <div className="dropdown">
                <div tabIndex={0} role="button" className="btn btn-ghost lg:hidden">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4 6h16M4 12h8m-8 6h16" />
                  </svg>
                </div>
                <ul tabIndex={0} className="menu menu-sm dropdown-content mt-3 z-[1] p-2 shadow bg-base-100 rounded-box w-52">
                  <li>
                    <button
                      className={currentView === 'todos' ? 'active' : ''}
                      onClick={() => setCurrentView('todos')}
                    >
                      Todo Management
                    </button>
                  </li>
                  <li>
                    <button
                      className={currentView === 'users' ? 'active' : ''}
                      onClick={() => setCurrentView('users')}
                    >
                      User Management
                    </button>
                  </li>
                </ul>
              </div>
              <a className="btn btn-ghost text-xl">Tini Melon</a>
            </div>
            <div className="navbar-center hidden lg:flex">
              <ul className="menu menu-horizontal px-1">
                <li>
                  <button
                    className={`btn btn-ghost ${currentView === 'todos' ? 'btn-active' : ''}`}
                    onClick={() => setCurrentView('todos')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
                    </svg>
                    Todo Management
                  </button>
                </li>
                <li>
                  <button
                    className={`btn btn-ghost ${currentView === 'users' ? 'btn-active' : ''}`}
                    onClick={() => setCurrentView('users')}
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                    </svg>
                    User Management
                  </button>
                </li>
              </ul>
            </div>
            <div className="navbar-end">
              <div className="dropdown dropdown-end">
                <div tabIndex={0} role="button" className="btn btn-ghost btn-circle avatar">
                  <div className="w-10 rounded-full">
                    <img alt="User avatar" src="https://img.daisyui.com/images/profile/demo/<EMAIL>" />
                  </div>
                </div>
                <ul tabIndex={0} className="mt-3 z-[1] p-2 shadow menu menu-sm dropdown-content bg-base-100 rounded-box w-52">
                  <li><a>Profile</a></li>
                  <li><a>Settings</a></li>
                  <li><a>Logout</a></li>
                </ul>
              </div>
            </div>
          </div>

          {/* Main Content */}
          <main className="min-h-[calc(100vh-4rem)]">
            {currentView === 'todos' && <TodoApp />}
            {currentView === 'users' && <UserManagementApp />}
          </main>
        </div>
      </Suspense>
    </ScopeProvider>
  );
}

export { App }
