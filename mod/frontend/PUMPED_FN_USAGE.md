# Pumped-fn Usage Guidelines

This document provides guidelines for using the pumped-fn pattern in the Kanban board implementation.

## Key Principles

1. **Always resolve controllers using useResolves or Resolves**
   - ❌ INCORRECT: `import { todosCtl } from '../pumped.todo'`
   - ✅ CORRECT: `const [todosCtl] = useResolves(app.todosCtl)`
   - ✅ CORRECT: `<Resolves e={[todosCtl]}>{([todosCtl]) => ...}</Resolves>`

2. **Use the unified import pattern**
   - ❌ INCORRECT: `import { selectedUser, userTodos, todosCtl } from '../pumped.todo'`
   - ✅ CORRECT: `import { app } from '../pumped.todo'`

3. **Properly distinguish between reactive and non-reactive dependencies**
   - Use `.reactive` for state that should trigger re-renders
   - Use direct references for controllers that don't need to trigger re-renders

## Examples

### Component with useResolves

```tsx
import React from 'react';
import { app } from '../pumped.todo';
import { useResolves } from '@pumped-fn/react';

const KanbanBoard: React.FC = () => {
  const [selectedUser, columns, todosCtl] = useResolves(
    app.selectedUser.reactive, 
    app.kanbanColumns.reactive,
    app.todosCtl
  );
  
  // Now you can use todosCtl safely
  const handleAction = () => {
    todosCtl.someAction();
  };
  
  return (
    // Component JSX
  );
};
```

### Component with Resolves

```tsx
import React from 'react';
import { app } from '../pumped.todo';
import { Resolves } from '@pumped-fn/react';

const TodoItem: React.FC<{ todo: Todo }> = ({ todo }) => {
  return (
    <Resolves e={[app.todosCtl]}>
      {([todosCtl]) => (
        <div>
          <button onClick={() => todosCtl.toggleTodoStatus(todo.id)}>
            Toggle
          </button>
        </div>
      )}
    </Resolves>
  );
};
```

## Benefits of This Pattern

1. **Predictable Reactivity**: Components only re-render when their dependencies change
2. **Type Safety**: TypeScript ensures correct usage of dependencies
3. **Testability**: Dependencies are explicit and can be mocked
4. **Performance**: Minimizes unnecessary re-renders

## Common Pitfalls to Avoid

1. **Direct Imports of Controllers**: Always resolve controllers using useResolves or Resolves
2. **Missing .reactive**: Forgetting to add .reactive to dependencies that should trigger re-renders
3. **Unnecessary .reactive**: Adding .reactive to controllers that don't need to trigger re-renders

