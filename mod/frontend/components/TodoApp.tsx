import React, { Suspense } from 'react';
import Header from './Header';
import HeroSection from './HeroSection';
import UserSelector from './UserSelector';
import AddTodoForm from './AddTodoForm';
import TodoList from './TodoList';

const TodoApp: React.FC = () => {
  return (
    <div className="min-h-screen bg-base-200 transition-colors duration-300">
      <Header />

      <main className="container mx-auto px-4 py-6 max-w-5xl">
        <HeroSection />

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-1">
            <div className="sticky top-4 space-y-6">
              <Suspense>
                <UserSelector />
              </Suspense>
              <Suspense>
                <AddTodoForm />
              </Suspense>
            </div>
          </div>

          <div className="lg:col-span-2">
            <Suspense>
              <TodoList />
            </Suspense>
          </div>
        </div>
      </main>

      <footer className="footer footer-center p-4 bg-base-300 text-base-content mt-12">
        <div>
          <p className="text-sm opacity-70">
            TaskBuddy &copy; {new Date().getFullYear()} - A clean, minimalist todo app
          </p>
        </div>
      </footer>
    </div>
  );
};

export default TodoApp;
