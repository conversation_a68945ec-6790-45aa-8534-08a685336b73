import React, { useState } from 'react';
import KanbanBoard from './KanbanBoard';
import { app } from '../pumped.todo';
import { useResolves } from '@pumped-fn/react';

const TodoList: React.FC = () => {
  const [selectedUser, userTodos] = useResolves(app.selectedUser.reactive, app.userTodos.reactive);
  const [filter, setFilter] = useState<'all' | 'active' | 'completed'>('all');
  
  const completedCount = userTodos.filter(todo => todo.completed).length;
  const activeCount = userTodos.length - completedCount;
  
  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-xl font-semibold flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          Tasks
        </h2>
        
        <div className="tabs tabs-boxed">
          <button 
            className={`tab ${filter === 'all' ? 'tab-active' : ''}`}
            onClick={() => setFilter('all')}
            aria-label="Show all tasks"
          >
            All ({userTodos.length})
          </button>
          <button 
            className={`tab ${filter === 'active' ? 'tab-active' : ''}`}
            onClick={() => setFilter('active')}
            aria-label="Show active tasks"
          >
            Active ({activeCount})
          </button>
          <button 
            className={`tab ${filter === 'completed' ? 'tab-active' : ''}`}
            onClick={() => setFilter('completed')}
            aria-label="Show completed tasks"
          >
            Completed ({completedCount})
          </button>
        </div>
      </div>
      
      <KanbanBoard />
    </div>
  );
};

export default TodoList;

