import { useResolves } from '@pumped-fn/react';
import React, { useState } from 'react';
import { app } from '../pumped.todo';

const AddTodoForm: React.FC = () => {
  const [selectedUser, todosCtl] = useResolves(app.selectedUser.reactive, app.todosCtl.reactive);
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [status, setStatus] = useState<'todo' | 'in-progress' | 'done'>('todo');
  const [isExpanded, setIsExpanded] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) return;
    
    todosCtl.addTodo({
      title: title.trim(),
      description: description.trim() || undefined,
      status
    });
    
    // Reset form
    setTitle('');
    setDescription('');
    setStatus('todo');
    setIsExpanded(false);
  };

  if (!selectedUser) {
    return (
      <div className="card bg-base-100 shadow-sm">
        <div className="card-body">
          <h2 className="card-title text-lg font-medium mb-2 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            Add New Task
          </h2>
          <p className="text-base-content/50 text-sm">Please select a user first</p>
        </div>
      </div>
    );
  }

  return (
    <div className="card bg-base-100 shadow-sm">
      <div className="card-body">
        <h2 className="card-title text-lg font-medium mb-4 flex items-center">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
          </svg>
          Add New Task
        </h2>
        
        <form onSubmit={handleSubmit}>
          <div className="form-control w-full">
            <input
              type="text"
              placeholder="What needs to be done?"
              className="input input-bordered w-full"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              onFocus={() => setIsExpanded(true)}
              aria-label="Task title"
            />
          </div>
          
          {isExpanded && (
            <>
              <div className="form-control w-full mt-3">
                <textarea
                  placeholder="Add a description (optional)"
                  className="textarea textarea-bordered w-full"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  rows={3}
                  aria-label="Task description"
                />
              </div>
              
              <div className="form-control w-full mt-3">
                <label className="label">
                  <span className="label-text">Status</span>
                </label>
                <select 
                  className="select select-bordered w-full" 
                  value={status}
                  onChange={(e) => setStatus(e.target.value as 'todo' | 'in-progress' | 'done')}
                  aria-label="Task status"
                >
                  <option value="todo">To Do</option>
                  <option value="in-progress">In Progress</option>
                  <option value="done">Done</option>
                </select>
              </div>
            </>
          )}
          
          <div className="form-control mt-4">
            <button
              type="submit"
              className="btn btn-primary"
              disabled={!title.trim()}
              aria-label="Add task"
            >
              Add Task
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default AddTodoForm;
