import React, { useState } from 'react';
import { Resolves } from '@pumped-fn/react';
import { userManagementApp } from '../../pumped.user-management';
import { UserManagement } from '../../../dual/user-management.types.zod';

interface UserStatusToggleProps {
  user: UserManagement;
}

const UserStatusToggle: React.FC<UserStatusToggleProps> = ({ user }) => {
  const [isToggling, setIsToggling] = useState(false);

  return (
    <Resolves e={[userManagementApp.actions]}>
      {([actions]) => {
        const handleToggle = async () => {
          if (isToggling) return;
          
          setIsToggling(true);
          try {
            await actions.toggleUserStatus(user.id);
          } catch (error) {
            // Error is handled in the action
          } finally {
            setIsToggling(false);
          }
        };

        return (
          <button
            className={`btn btn-xs ${
              user.status === 'active' 
                ? 'btn-success' 
                : 'btn-warning'
            } ${isToggling ? 'loading' : ''}`}
            onClick={handleToggle}
            disabled={isToggling}
            title={`Click to ${user.status === 'active' ? 'deactivate' : 'activate'} user`}
          >
            {!isToggling && (
              <>
                {user.status === 'active' ? (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Active
                  </>
                ) : (
                  <>
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    Inactive
                  </>
                )}
              </>
            )}
          </button>
        );
      }}
    </Resolves>
  );
};

export default UserStatusToggle;
