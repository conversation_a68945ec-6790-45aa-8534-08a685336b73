import React, { useState, useEffect } from 'react';
import { Resolves } from '@pumped-fn/react';
import { userManagementApp } from '../../pumped.user-management';
import { UserManagement, UserUpdate, UserUpdateSchema, USER_ROLES, USER_STATUSES } from '../../../dual/user-management.types.zod';

interface EditUserFormProps {
  user: UserManagement;
}

const EditUserForm: React.FC<EditUserFormProps> = ({ user }) => {
  const [formData, setFormData] = useState<UserUpdate>({
    firstName: user.firstName,
    lastName: user.lastName,
    email: user.email,
    role: user.role,
    status: user.status,
    avatar: user.avatar || '',
  });

  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Reset form when user changes
  useEffect(() => {
    setFormData({
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      role: user.role,
      status: user.status,
      avatar: user.avatar || '',
    });
    setErrors({});
  }, [user]);

  return (
    <Resolves e={[
      userManagementApp.state.reactive,
      userManagementApp.actions,
      userManagementApp.modalActions
    ]}>
      {([state, actions, modalActions]) => {
        const validateForm = (): boolean => {
          try {
            UserUpdateSchema.parse(formData);
            setErrors({});
            return true;
          } catch (error: any) {
            const newErrors: Record<string, string> = {};
            if (error.errors) {
              error.errors.forEach((err: any) => {
                const field = err.path[0];
                newErrors[field] = err.message;
              });
            }
            setErrors(newErrors);
            return false;
          }
        };

        const handleSubmit = async (e: React.FormEvent) => {
          e.preventDefault();
          
          if (!validateForm()) return;
          
          setIsSubmitting(true);
          try {
            await actions.updateUser(user.id, formData);
            // Modal will be closed by the action
          } catch (error) {
            // Error is handled in the action
          } finally {
            setIsSubmitting(false);
          }
        };

        const handleInputChange = (field: keyof UserUpdate, value: string) => {
          setFormData(prev => ({ ...prev, [field]: value }));
          // Clear error for this field
          if (errors[field]) {
            setErrors(prev => ({ ...prev, [field]: '' }));
          }
        };

        const hasChanges = () => {
          return (
            formData.firstName !== user.firstName ||
            formData.lastName !== user.lastName ||
            formData.email !== user.email ||
            formData.role !== user.role ||
            formData.status !== user.status ||
            formData.avatar !== (user.avatar || '')
          );
        };

        return (
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* User Info Header */}
            <div className="flex items-center gap-4 p-4 bg-base-200 rounded-lg">
              <div className="avatar">
                <div className="w-16 h-16 mask mask-squircle">
                  {user.avatar ? (
                    <img src={user.avatar} alt={`${user.firstName} ${user.lastName}`} />
                  ) : (
                    <div className="bg-primary/10 flex items-center justify-center">
                      <span className="text-primary font-semibold text-lg">
                        {user.firstName[0]}{user.lastName[0]}
                      </span>
                    </div>
                  )}
                </div>
              </div>
              <div>
                <h4 className="font-semibold">Editing: {user.firstName} {user.lastName}</h4>
                <p className="text-sm text-base-content/70">User ID: {user.id}</p>
                <p className="text-sm text-base-content/70">
                  Created: {new Date(user.createdAt).toLocaleDateString()}
                </p>
              </div>
            </div>

            {/* First Name */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">First Name *</span>
              </label>
              <input
                type="text"
                className={`input input-bordered w-full ${errors.firstName ? 'input-error' : ''}`}
                placeholder="Enter first name"
                value={formData.firstName || ''}
                onChange={(e) => handleInputChange('firstName', e.target.value)}
                disabled={isSubmitting}
                required
              />
              {errors.firstName && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.firstName}</span>
                </label>
              )}
            </div>

            {/* Last Name */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Last Name *</span>
              </label>
              <input
                type="text"
                className={`input input-bordered w-full ${errors.lastName ? 'input-error' : ''}`}
                placeholder="Enter last name"
                value={formData.lastName || ''}
                onChange={(e) => handleInputChange('lastName', e.target.value)}
                disabled={isSubmitting}
                required
              />
              {errors.lastName && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.lastName}</span>
                </label>
              )}
            </div>

            {/* Email */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Email *</span>
              </label>
              <input
                type="email"
                className={`input input-bordered w-full ${errors.email ? 'input-error' : ''}`}
                placeholder="Enter email address"
                value={formData.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value)}
                disabled={isSubmitting}
                required
              />
              {errors.email && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.email}</span>
                </label>
              )}
            </div>

            {/* Role and Status Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Role */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Role</span>
                </label>
                <select
                  className={`select select-bordered w-full ${errors.role ? 'select-error' : ''}`}
                  value={formData.role || 'user'}
                  onChange={(e) => handleInputChange('role', e.target.value)}
                  disabled={isSubmitting}
                >
                  {USER_ROLES.map(role => (
                    <option key={role.value} value={role.value}>
                      {role.label}
                    </option>
                  ))}
                </select>
                {errors.role && (
                  <label className="label">
                    <span className="label-text-alt text-error">{errors.role}</span>
                  </label>
                )}
              </div>

              {/* Status */}
              <div className="form-control">
                <label className="label">
                  <span className="label-text">Status</span>
                </label>
                <select
                  className={`select select-bordered w-full ${errors.status ? 'select-error' : ''}`}
                  value={formData.status || 'active'}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  disabled={isSubmitting}
                >
                  {USER_STATUSES.map(status => (
                    <option key={status.value} value={status.value}>
                      {status.label}
                    </option>
                  ))}
                </select>
                {errors.status && (
                  <label className="label">
                    <span className="label-text-alt text-error">{errors.status}</span>
                  </label>
                )}
              </div>
            </div>

            {/* Avatar URL */}
            <div className="form-control">
              <label className="label">
                <span className="label-text">Avatar URL (optional)</span>
              </label>
              <input
                type="url"
                className={`input input-bordered w-full ${errors.avatar ? 'input-error' : ''}`}
                placeholder="https://example.com/avatar.jpg"
                value={formData.avatar || ''}
                onChange={(e) => handleInputChange('avatar', e.target.value)}
                disabled={isSubmitting}
              />
              {errors.avatar && (
                <label className="label">
                  <span className="label-text-alt text-error">{errors.avatar}</span>
                </label>
              )}
            </div>

            {/* Form Actions */}
            <div className="modal-action">
              <button
                type="button"
                className="btn btn-ghost"
                onClick={modalActions.closeModal}
                disabled={isSubmitting}
              >
                Cancel
              </button>
              <button
                type="submit"
                className={`btn btn-primary ${isSubmitting ? 'loading' : ''}`}
                disabled={isSubmitting || !hasChanges()}
              >
                {isSubmitting ? 'Updating...' : 'Update User'}
              </button>
            </div>

            {/* Global Error */}
            {state.error && (
              <div className="alert alert-error">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{state.error}</span>
              </div>
            )}
          </form>
        );
      }}
    </Resolves>
  );
};

export default EditUserForm;
