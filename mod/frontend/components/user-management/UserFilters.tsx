import React, { useState, useEffect } from 'react';
import { Resolves } from '@pumped-fn/react';
import { userManagementApp } from '../../pumped.user-management';
import { USER_ROLES, USER_STATUSES } from '../../../dual/user-management.types.zod';

const UserFilters: React.FC = () => {
  return (
    <Resolves e={[
      userManagementApp.state.reactive,
      userManagementApp.actions
    ]}>
      {([state, actions]) => {
        const [searchInput, setSearchInput] = useState(state.filters.search);

        // Debounce search input
        useEffect(() => {
          const timer = setTimeout(() => {
            if (searchInput !== state.filters.search) {
              actions.updateFilters({ search: searchInput });
              actions.loadUsers();
            }
          }, 300);

          return () => clearTimeout(timer);
        }, [searchInput, state.filters.search, actions]);

        const handleRoleChange = (role: string) => {
          const newRole = role === '' ? undefined : role as 'admin' | 'user' | 'moderator';
          actions.updateFilters({ role: newRole });
          actions.loadUsers();
        };

        const handleStatusChange = (status: string) => {
          const newStatus = status === '' ? undefined : status as 'active' | 'inactive';
          actions.updateFilters({ status: newStatus });
          actions.loadUsers();
        };

        const clearFilters = () => {
          setSearchInput('');
          actions.updateFilters({ search: '', role: undefined, status: undefined });
          actions.loadUsers();
        };

        const hasActiveFilters = state.filters.search || state.filters.role || state.filters.status;

        return (
          <div className="card bg-base-100 shadow-sm mb-6">
            <div className="card-body p-4">
              <div className="flex flex-col lg:flex-row gap-4">
                {/* Search Input */}
                <div className="form-control flex-1">
                  <label className="input input-bordered flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 opacity-70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                    <input
                      type="text"
                      className="grow"
                      placeholder="Search users by name or email..."
                      value={searchInput}
                      onChange={(e) => setSearchInput(e.target.value)}
                    />
                    {searchInput && (
                      <button
                        className="btn btn-ghost btn-xs"
                        onClick={() => setSearchInput('')}
                      >
                        ✕
                      </button>
                    )}
                  </label>
                </div>

                {/* Role Filter */}
                <div className="form-control w-full lg:w-48">
                  <select
                    className="select select-bordered w-full"
                    value={state.filters.role || ''}
                    onChange={(e) => handleRoleChange(e.target.value)}
                  >
                    <option value="">All Roles</option>
                    {USER_ROLES.map(role => (
                      <option key={role.value} value={role.value}>
                        {role.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Status Filter */}
                <div className="form-control w-full lg:w-48">
                  <select
                    className="select select-bordered w-full"
                    value={state.filters.status || ''}
                    onChange={(e) => handleStatusChange(e.target.value)}
                  >
                    <option value="">All Statuses</option>
                    {USER_STATUSES.map(status => (
                      <option key={status.value} value={status.value}>
                        {status.label}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Clear Filters Button */}
                {hasActiveFilters && (
                  <div className="form-control">
                    <button
                      className="btn btn-ghost"
                      onClick={clearFilters}
                      title="Clear all filters"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                      Clear
                    </button>
                  </div>
                )}
              </div>

              {/* Active Filters Display */}
              {hasActiveFilters && (
                <div className="flex flex-wrap gap-2 mt-4">
                  <span className="text-sm text-base-content/70">Active filters:</span>
                  
                  {state.filters.search && (
                    <div className="badge badge-primary gap-2">
                      Search: "{state.filters.search}"
                      <button
                        className="btn btn-ghost btn-xs"
                        onClick={() => {
                          setSearchInput('');
                          actions.updateFilters({ search: '' });
                          actions.loadUsers();
                        }}
                      >
                        ✕
                      </button>
                    </div>
                  )}
                  
                  {state.filters.role && (
                    <div className="badge badge-secondary gap-2">
                      Role: {USER_ROLES.find(r => r.value === state.filters.role)?.label}
                      <button
                        className="btn btn-ghost btn-xs"
                        onClick={() => handleRoleChange('')}
                      >
                        ✕
                      </button>
                    </div>
                  )}
                  
                  {state.filters.status && (
                    <div className="badge badge-accent gap-2">
                      Status: {USER_STATUSES.find(s => s.value === state.filters.status)?.label}
                      <button
                        className="btn btn-ghost btn-xs"
                        onClick={() => handleStatusChange('')}
                      >
                        ✕
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        );
      }}
    </Resolves>
  );
};

export default UserFilters;
