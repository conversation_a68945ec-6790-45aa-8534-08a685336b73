import React, { useEffect } from 'react';
import { Resolves } from '@pumped-fn/react';
import { userManagementApp } from '../../pumped.user-management';
import UserFilters from './UserFilters';
import UserTable from './UserTable';
import UserModal from './UserModal';

const UserManagementApp: React.FC = () => {
  return (
    <Resolves e={[
      userManagementApp.state.reactive,
      userManagementApp.modalState.reactive,
      userManagementApp.actions,
      userManagementApp.modalActions
    ]}>
      {([state, modalState, actions, modalActions]) => {
        // Load users on component mount
        useEffect(() => {
          actions.loadUsers();
        }, []);

        return (
          <div className="container mx-auto px-4 py-6 max-w-7xl">
            {/* Header */}
            <div className="flex justify-between items-center mb-6">
              <div>
                <h1 className="text-3xl font-bold text-base-content">User Management</h1>
                <p className="text-base-content/70 mt-1">
                  Manage users, roles, and permissions
                </p>
              </div>
              
              <button 
                className="btn btn-primary"
                onClick={modalActions.openCreateModal}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                </svg>
                Add User
              </button>
            </div>

            {/* Error Alert */}
            {state.error && (
              <div className="alert alert-error mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="stroke-current shrink-0 h-6 w-6" fill="none" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>{state.error}</span>
                <button 
                  className="btn btn-sm btn-ghost"
                  onClick={actions.clearError}
                >
                  ✕
                </button>
              </div>
            )}

            {/* Filters */}
            <UserFilters />

            {/* Stats Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
              <div className="card bg-base-100 shadow-sm">
                <div className="card-body p-4">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm text-base-content/70">Total Users</p>
                      <p className="text-2xl font-bold">{state.pagination.total}</p>
                    </div>
                    <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card bg-base-100 shadow-sm">
                <div className="card-body p-4">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm text-base-content/70">Active Users</p>
                      <p className="text-2xl font-bold text-success">
                        {state.users.filter(u => u.status === 'active').length}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-success/10 rounded-lg flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-success" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card bg-base-100 shadow-sm">
                <div className="card-body p-4">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm text-base-content/70">Inactive Users</p>
                      <p className="text-2xl font-bold text-warning">
                        {state.users.filter(u => u.status === 'inactive').length}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-warning/10 rounded-lg flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-warning" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>

              <div className="card bg-base-100 shadow-sm">
                <div className="card-body p-4">
                  <div className="flex items-center">
                    <div className="flex-1">
                      <p className="text-sm text-base-content/70">Admins</p>
                      <p className="text-2xl font-bold text-info">
                        {state.users.filter(u => u.role === 'admin').length}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-info/10 rounded-lg flex items-center justify-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-info" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                      </svg>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* User Table */}
            <UserTable />

            {/* Modal */}
            <UserModal />
          </div>
        );
      }}
    </Resolves>
  );
};

export default UserManagementApp;
