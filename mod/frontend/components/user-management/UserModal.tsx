import React, { useEffect } from 'react';
import { Resolves } from '@pumped-fn/react';
import { userManagementApp } from '../../pumped.user-management';
import CreateUserForm from './CreateUserForm';
import EditUserForm from './EditUserForm';

const UserModal: React.FC = () => {
  return (
    <Resolves e={[
      userManagementApp.modalState.reactive,
      userManagementApp.modalActions
    ]}>
      {([modalState, modalActions]) => {
        // Close modal on Escape key
        useEffect(() => {
          const handleEscape = (e: KeyboardEvent) => {
            if (e.key === 'Escape' && modalState.isOpen) {
              modalActions.closeModal();
            }
          };

          document.addEventListener('keydown', handleEscape);
          return () => document.removeEventListener('keydown', handleEscape);
        }, [modalState.isOpen, modalActions]);

        if (!modalState.isOpen) return null;

        return (
          <dialog className="modal modal-open">
            <div className="modal-box w-11/12 max-w-2xl">
              {/* Modal Header */}
              <div className="flex justify-between items-center mb-6">
                <h3 className="font-bold text-lg">
                  {modalState.mode === 'create' ? 'Add New User' : 'Edit User'}
                </h3>
                <button
                  className="btn btn-sm btn-circle btn-ghost"
                  onClick={modalActions.closeModal}
                >
                  ✕
                </button>
              </div>

              {/* Modal Content */}
              <div className="py-4">
                {modalState.mode === 'create' ? (
                  <CreateUserForm />
                ) : (
                  <EditUserForm user={modalState.selectedUser!} />
                )}
              </div>
            </div>

            {/* Modal Backdrop */}
            <form method="dialog" className="modal-backdrop">
              <button onClick={modalActions.closeModal}>close</button>
            </form>
          </dialog>
        );
      }}
    </Resolves>
  );
};

export default UserModal;
