import React from 'react';
import { Resolves } from '@pumped-fn/react';
import { userManagementApp } from '../../pumped.user-management';
import { UserManagement } from '../../../dual/user-management.types.zod';
import UserStatusToggle from './UserStatusToggle';

const UserTable: React.FC = () => {
  return (
    <Resolves e={[
      userManagementApp.state.reactive,
      userManagementApp.actions,
      userManagementApp.modalActions
    ]}>
      {([state, actions, modalActions]) => {
        const formatDate = (date: Date) => {
          return new Intl.DateTimeFormat('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
          }).format(new Date(date));
        };

        const getRoleBadgeClass = (role: string) => {
          switch (role) {
            case 'admin': return 'badge-error';
            case 'moderator': return 'badge-warning';
            case 'user': return 'badge-info';
            default: return 'badge-ghost';
          }
        };

        const handleEdit = (user: UserManagement) => {
          modalActions.openEditModal(user);
        };

        const handleDelete = async (user: UserManagement) => {
          if (window.confirm(`Are you sure you want to delete ${user.firstName} ${user.lastName}?`)) {
            try {
              await actions.deleteUser(user.id);
            } catch (error) {
              // Error is handled in the action
            }
          }
        };

        const handlePageChange = (newPage: number) => {
          actions.updatePagination({ page: newPage });
          actions.loadUsers();
        };

        if (state.loading && state.users.length === 0) {
          return (
            <div className="card bg-base-100 shadow-sm">
              <div className="card-body">
                <div className="flex justify-center items-center py-12">
                  <span className="loading loading-spinner loading-lg"></span>
                </div>
              </div>
            </div>
          );
        }

        return (
          <div className="card bg-base-100 shadow-sm">
            <div className="card-body p-0">
              {/* Table */}
              <div className="overflow-x-auto">
                <table className="table table-zebra">
                  <thead>
                    <tr>
                      <th>User</th>
                      <th>Role</th>
                      <th>Status</th>
                      <th>Created</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {state.users.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="text-center py-12">
                          <div className="flex flex-col items-center gap-2">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-base-content/30" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                            </svg>
                            <p className="text-base-content/70">No users found</p>
                            <button 
                              className="btn btn-primary btn-sm"
                              onClick={modalActions.openCreateModal}
                            >
                              Add First User
                            </button>
                          </div>
                        </td>
                      </tr>
                    ) : (
                      state.users.map((user) => (
                        <tr key={user.id}>
                          {/* User Info */}
                          <td>
                            <div className="flex items-center gap-3">
                              <div className="avatar">
                                <div className="w-12 h-12 mask mask-squircle">
                                  {user.avatar ? (
                                    <img src={user.avatar} alt={`${user.firstName} ${user.lastName}`} />
                                  ) : (
                                    <div className="bg-primary/10 flex items-center justify-center">
                                      <span className="text-primary font-semibold">
                                        {user.firstName[0]}{user.lastName[0]}
                                      </span>
                                    </div>
                                  )}
                                </div>
                              </div>
                              <div>
                                <div className="font-bold">{user.firstName} {user.lastName}</div>
                                <div className="text-sm opacity-50">{user.email}</div>
                              </div>
                            </div>
                          </td>

                          {/* Role */}
                          <td>
                            <span className={`badge ${getRoleBadgeClass(user.role)} badge-sm`}>
                              {user.role}
                            </span>
                          </td>

                          {/* Status */}
                          <td>
                            <UserStatusToggle user={user} />
                          </td>

                          {/* Created Date */}
                          <td>
                            <div className="text-sm">
                              {formatDate(user.createdAt)}
                            </div>
                          </td>

                          {/* Actions */}
                          <td>
                            <div className="flex gap-2">
                              <button
                                className="btn btn-ghost btn-xs"
                                onClick={() => handleEdit(user)}
                                title="Edit user"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                </svg>
                              </button>
                              
                              <button
                                className="btn btn-ghost btn-xs text-error hover:bg-error hover:text-error-content"
                                onClick={() => handleDelete(user)}
                                title="Delete user"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>

              {/* Pagination */}
              {state.pagination.totalPages > 1 && (
                <div className="flex justify-between items-center p-4 border-t border-base-300">
                  <div className="text-sm text-base-content/70">
                    Showing {((state.pagination.page - 1) * state.pagination.limit) + 1} to{' '}
                    {Math.min(state.pagination.page * state.pagination.limit, state.pagination.total)} of{' '}
                    {state.pagination.total} users
                  </div>
                  
                  <div className="join">
                    <button
                      className="join-item btn btn-sm"
                      disabled={state.pagination.page === 1 || state.loading}
                      onClick={() => handlePageChange(state.pagination.page - 1)}
                    >
                      «
                    </button>
                    
                    {Array.from({ length: state.pagination.totalPages }, (_, i) => i + 1)
                      .filter(page => 
                        page === 1 || 
                        page === state.pagination.totalPages || 
                        Math.abs(page - state.pagination.page) <= 2
                      )
                      .map((page, index, array) => (
                        <React.Fragment key={page}>
                          {index > 0 && array[index - 1] !== page - 1 && (
                            <button className="join-item btn btn-sm btn-disabled">...</button>
                          )}
                          <button
                            className={`join-item btn btn-sm ${
                              page === state.pagination.page ? 'btn-active' : ''
                            }`}
                            disabled={state.loading}
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </button>
                        </React.Fragment>
                      ))}
                    
                    <button
                      className="join-item btn btn-sm"
                      disabled={state.pagination.page === state.pagination.totalPages || state.loading}
                      onClick={() => handlePageChange(state.pagination.page + 1)}
                    >
                      »
                    </button>
                  </div>
                </div>
              )}

              {/* Loading Overlay */}
              {state.loading && state.users.length > 0 && (
                <div className="absolute inset-0 bg-base-100/50 flex items-center justify-center">
                  <span className="loading loading-spinner loading-lg"></span>
                </div>
              )}
            </div>
          </div>
        );
      }}
    </Resolves>
  );
};

export default UserTable;
