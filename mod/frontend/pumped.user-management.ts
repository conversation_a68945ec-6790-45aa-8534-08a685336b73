import { provide, derive } from "@pumped-fn/core-next";
import { logger } from "@/base/dual/logger";
import { 
  UserManagement, 
  UserCreate, 
  UserUpdate, 
  UserListQuery, 
  UserListResponse,
  UserStatusToggle 
} from "../dual/user-management.types.zod";

// State for user management
export const userManagementState = provide(() => ({
  users: [] as UserManagement[],
  loading: false,
  error: null as string | null,
  pagination: {
    page: 1,
    limit: 10,
    total: 0,
    totalPages: 0,
  },
  filters: {
    search: '',
    role: undefined as 'admin' | 'user' | 'moderator' | undefined,
    status: undefined as 'active' | 'inactive' | undefined,
  },
}));

// Modal state
export const modalState = provide(() => ({
  isOpen: false,
  mode: 'create' as 'create' | 'edit',
  selectedUser: null as UserManagement | null,
}));

// Derived state for filtered users (client-side filtering for better UX)
export const filteredUsers = derive(
  [userManagementState.reactive],
  ([state]) => {
    let filtered = [...state.users];
    
    // Apply search filter
    if (state.filters.search) {
      const searchTerm = state.filters.search.toLowerCase();
      filtered = filtered.filter(user => 
        user.firstName.toLowerCase().includes(searchTerm) ||
        user.lastName.toLowerCase().includes(searchTerm) ||
        user.email.toLowerCase().includes(searchTerm)
      );
    }
    
    // Apply role filter
    if (state.filters.role) {
      filtered = filtered.filter(user => user.role === state.filters.role);
    }
    
    // Apply status filter
    if (state.filters.status) {
      filtered = filtered.filter(user => user.status === state.filters.status);
    }
    
    return filtered;
  }
);

// API helper functions
const apiCall = async (url: string, options: RequestInit = {}) => {
  const response = await fetch(url, {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
    ...options,
  });
  
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
    throw new Error(errorData.error || `HTTP ${response.status}`);
  }
  
  return response.json();
};

// User management actions
export const userManagementActions = derive(
  [userManagementState.static, modalState.static, logger('userManagement')],
  ([stateCtl, modalCtl, logger]) => ({
    // Load users with pagination and filtering
    loadUsers: async (query?: Partial<UserListQuery>) => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        const currentState = stateCtl.get();
        const searchParams = new URLSearchParams();
        
        // Build query parameters
        const finalQuery = {
          page: currentState.pagination.page,
          limit: currentState.pagination.limit,
          ...currentState.filters,
          ...query,
        };
        
        Object.entries(finalQuery).forEach(([key, value]) => {
          if (value !== undefined && value !== '') {
            searchParams.append(key, String(value));
          }
        });
        
        logger.info('Loading users with query:', finalQuery);
        
        const response: UserListResponse = await apiCall(`/api/users?${searchParams}`);
        
        stateCtl.update(state => ({
          ...state,
          users: response.users,
          pagination: response.pagination,
          loading: false,
        }));
        
        logger.info('Users loaded successfully:', response.users.length);
      } catch (error) {
        logger.error('Error loading users:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to load users',
        }));
      }
    },

    // Create a new user
    createUser: async (userData: UserCreate) => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        logger.info('Creating user:', userData);
        
        const newUser: UserManagement = await apiCall('/api/users', {
          method: 'POST',
          body: JSON.stringify(userData),
        });
        
        stateCtl.update(state => ({
          ...state,
          users: [...state.users, newUser],
          loading: false,
        }));
        
        // Close modal
        modalCtl.update(modal => ({ ...modal, isOpen: false, selectedUser: null }));
        
        logger.info('User created successfully:', newUser);
        return newUser;
      } catch (error) {
        logger.error('Error creating user:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to create user',
        }));
        throw error;
      }
    },

    // Update an existing user
    updateUser: async (id: number, updates: UserUpdate) => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        logger.info('Updating user:', id, updates);
        
        const updatedUser: UserManagement = await apiCall(`/api/users/${id}`, {
          method: 'PUT',
          body: JSON.stringify(updates),
        });
        
        stateCtl.update(state => ({
          ...state,
          users: state.users.map(user => user.id === id ? updatedUser : user),
          loading: false,
        }));
        
        // Close modal
        modalCtl.update(modal => ({ ...modal, isOpen: false, selectedUser: null }));
        
        logger.info('User updated successfully:', updatedUser);
        return updatedUser;
      } catch (error) {
        logger.error('Error updating user:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to update user',
        }));
        throw error;
      }
    },

    // Toggle user status
    toggleUserStatus: async (id: number) => {
      try {
        logger.info('Toggling user status:', id);
        
        const result: UserStatusToggle = await apiCall(`/api/users/${id}/status`, {
          method: 'PATCH',
        });
        
        stateCtl.update(state => ({
          ...state,
          users: state.users.map(user => 
            user.id === id ? { ...user, status: result.status, updatedAt: new Date() } : user
          ),
        }));
        
        logger.info('User status toggled successfully:', result);
        return result;
      } catch (error) {
        logger.error('Error toggling user status:', error);
        stateCtl.update(state => ({
          ...state,
          error: error instanceof Error ? error.message : 'Failed to toggle user status',
        }));
        throw error;
      }
    },

    // Delete a user (soft delete)
    deleteUser: async (id: number) => {
      try {
        stateCtl.update(state => ({ ...state, loading: true, error: null }));
        
        logger.info('Deleting user:', id);
        
        await apiCall(`/api/users/${id}`, {
          method: 'DELETE',
        });
        
        stateCtl.update(state => ({
          ...state,
          users: state.users.map(user => 
            user.id === id ? { ...user, status: 'inactive' as const, updatedAt: new Date() } : user
          ),
          loading: false,
        }));
        
        logger.info('User deleted successfully');
      } catch (error) {
        logger.error('Error deleting user:', error);
        stateCtl.update(state => ({
          ...state,
          loading: false,
          error: error instanceof Error ? error.message : 'Failed to delete user',
        }));
        throw error;
      }
    },

    // Update filters
    updateFilters: (filters: Partial<{
      search: string;
      role: 'admin' | 'user' | 'moderator' | undefined;
      status: 'active' | 'inactive' | undefined;
    }>) => {
      stateCtl.update(state => ({
        ...state,
        filters: { ...state.filters, ...filters },
        pagination: { ...state.pagination, page: 1 }, // Reset to first page
      }));
    },

    // Update pagination
    updatePagination: (pagination: Partial<{
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    }>) => {
      stateCtl.update(state => ({
        ...state,
        pagination: { ...state.pagination, ...pagination },
      }));
    },

    // Clear error
    clearError: () => {
      stateCtl.update(state => ({ ...state, error: null }));
    },
  })
);

// Modal actions
export const modalActions = derive(
  [modalState.static],
  ([modalCtl]) => ({
    openCreateModal: () => {
      modalCtl.update(modal => ({
        ...modal,
        isOpen: true,
        mode: 'create',
        selectedUser: null,
      }));
    },

    openEditModal: (user: UserManagement) => {
      modalCtl.update(modal => ({
        ...modal,
        isOpen: true,
        mode: 'edit',
        selectedUser: user,
      }));
    },

    closeModal: () => {
      modalCtl.update(modal => ({
        ...modal,
        isOpen: false,
        selectedUser: null,
      }));
    },
  })
);

// Export unified app object
export const userManagementApp = {
  state: userManagementState,
  modalState,
  filteredUsers,
  actions: userManagementActions,
  modalActions,
};
