import { User, Todo } from './types';

// Mock Users
export const mockUsers: User[] = [
  {
    id: 1,
    email: '<EMAIL>',
    name: '<PERSON>',
    createdAt: new Date('2023-01-15'),
    updatedAt: new Date('2023-01-15')
  },
  {
    id: 2,
    email: '<EMAIL>',
    name: '<PERSON>',
    createdAt: new Date('2023-02-20'),
    updatedAt: new Date('2023-02-20')
  },
  {
    id: 3,
    email: '<EMAIL>',
    name: '<PERSON>',
    createdAt: new Date('2023-03-10'),
    updatedAt: new Date('2023-03-10')
  }
];

// Mock Todos
export const mockTodos: Todo[] = [
  {
    id: 1,
    userId: 1,
    title: 'Complete project proposal',
    description: 'Finish the Q3 project proposal for client review',
    completed: false,
    status: 'todo',
    createdAt: new Date('2023-05-01'),
    updatedAt: new Date('2023-05-01')
  },
  {
    id: 2,
    userId: 1,
    title: 'Schedule team meeting',
    description: 'Set up weekly team sync for project updates',
    completed: true,
    status: 'done',
    createdAt: new Date('2023-05-02'),
    updatedAt: new Date('2023-05-03')
  },
  {
    id: 3,
    userId: 1,
    title: 'Research new technologies',
    description: 'Look into React 18 features for potential upgrade',
    completed: false,
    status: 'in-progress',
    createdAt: new Date('2023-05-05'),
    updatedAt: new Date('2023-05-05')
  },
  {
    id: 4,
    userId: 2,
    title: 'Review design mockups',
    description: 'Provide feedback on new UI design concepts',
    completed: false,
    status: 'todo',
    createdAt: new Date('2023-05-01'),
    updatedAt: new Date('2023-05-01')
  },
  {
    id: 5,
    userId: 2,
    title: 'Update documentation',
    description: 'Update API documentation with new endpoints',
    completed: true,
    status: 'done',
    createdAt: new Date('2023-05-03'),
    updatedAt: new Date('2023-05-04')
  },
  {
    id: 6,
    userId: 3,
    title: 'Fix navigation bug',
    description: 'Address the navigation issue on mobile devices',
    completed: false,
    status: 'in-progress',
    createdAt: new Date('2023-05-02'),
    updatedAt: new Date('2023-05-02')
  },
  {
    id: 7,
    userId: 3,
    title: 'Implement dark mode',
    description: 'Add dark mode support to the application',
    completed: true,
    status: 'done',
    createdAt: new Date('2023-05-04'),
    updatedAt: new Date('2023-05-06')
  }
];
