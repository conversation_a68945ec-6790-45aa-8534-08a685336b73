import { getElectricConfig } from "@/base/backend/config"
import type { selects } from "@/base/db/index.types"
import { todos, users } from "@/base/db/schema"
import { logger } from "@/base/dual/logger"
import { Row, Shape, ShapeStream, ShapeStreamOptions } from "@electric-sql/client"
import { derive, provide, type Core } from "@pumped-fn/core-next"

const config = provide(() => getElectricConfig())

const shapeCache = new Map<string, Core.Executor<Shape<Row<unknown>>>>()

const createShape = <T>(arg: ShapeStreamOptions['params']) => {
  const shapeKey = `${arg?.table}::[${arg?.where ?? '*'}::${arg?.params ?? '-'}]`
  if (shapeCache.has(shapeKey)) {
    return shapeCache.get(shapeKey)!
  }

  const newShape = derive(
    [config, logger(shapeKey)],
    async ([config, logger], ctl) => {
      const endpoint = `http://${config.host}:${config.port}/v1/shape`
      logger.info(`Creating shape for ${shapeKey} at ${endpoint}`)

      const stream = new ShapeStream<Row<T>>({
        params: arg,
        url: `http://${config.host}:${config.port}/v1/shape`,
      })

      const shape = new Shape(stream)
      logger.debug(`Awaiting shape to be ready`)

      await shape.rows
      logger.info(`Shape is ready`)

      shape.subscribe(updates => {
        logger.info(`Shape updated: %O`, updates)
      })

      ctl.cleanup(() => {
        shape.unsubscribeAll()
      })

      return shape
    })

  shapeCache.set(shapeKey, newShape)

  return newShape
}

export const userShape = (userId: number) => createShape<selects['users']>({
  table: 'users',
  where: `${users.id.name.toString()} = $1`,
  params: [userId.toString()]
})

export const todoShape = (userId: number) => createShape<selects['todos']>({
  table: "todos",
  where: `${todos.userId.name.toString()} = $1`,
  params: [userId.toString()]
})