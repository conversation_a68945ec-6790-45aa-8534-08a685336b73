import { todoShape, userShape } from "@/mod/backend/shapes"
import { logger } from "@/base/dual/logger"
import { connection } from "@/base/db"
import { users } from "@/mod/backend/schema"
import { derive } from "@pumped-fn/core-next"
import { BunRequest } from "bun"
import { eq } from "drizzle-orm"

export const shape = derive(
  [connection, logger('shapes')],
  ([db, logger], ctl) => {
    return async (request: BunRequest<string>) => {
      // we'll need to handle shape for the current user
      const url = new URL(request.url)
      const userId = request.headers.get("x-user-id") || url.searchParams.get("userId")
      logger.info(`Received shape request for userId: ${userId}`)

      if (!userId) {
        logger.error("Missing x-user-id header")
        return new Response("Missing x-user-id header", { status: 400 })
      }

      const userCount = await db.$count(users, eq(users.id, Number(userId)))
      if (userCount === 0) {
        logger.error(`User with id ${userId} not found`)
        return new Response(`User with id ${userId} not found`, { status: 404 })
      }

      logger.info(`Serving shapes for user ${userId}`)

      const [rUserShape, rTodoShape] = await Promise.all([
        ctl.scope.resolve(userShape(Number(userId))),
        ctl.scope.resolve(todoShape(Number(userId)))
      ])

      return Response.json({
        user: rUserShape.currentRows,
        todos: rTodoShape.currentRows
      })
    }
  }
)