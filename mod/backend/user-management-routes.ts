import { 
  UserManagementSchema, 
  UserCreateSchema, 
  UserUpdateSchema, 
  UserListQuerySchema,
  UserListResponseSchema,
  UserStatusToggleSchema,
  UserManagement 
} from '../dual/user-management.types.zod';
import { mockUsers } from '../frontend/mockUserData';

// In-memory storage for development (replace with database in production)
let users: UserManagement[] = [...mockUsers];
let nextId = Math.max(...users.map(u => u.id)) + 1;

// Helper function to filter users based on query parameters
function filterUsers(query: any) {
  let filteredUsers = [...users];

  // Search filter
  if (query.search) {
    const searchTerm = query.search.toLowerCase();
    filteredUsers = filteredUsers.filter(user => 
      user.firstName.toLowerCase().includes(searchTerm) ||
      user.lastName.toLowerCase().includes(searchTerm) ||
      user.email.toLowerCase().includes(searchTerm)
    );
  }

  // Role filter
  if (query.role) {
    filteredUsers = filteredUsers.filter(user => user.role === query.role);
  }

  // Status filter
  if (query.status) {
    filteredUsers = filteredUsers.filter(user => user.status === query.status);
  }

  return filteredUsers;
}

// Helper function to paginate results
function paginateUsers(users: UserManagement[], page: number, limit: number) {
  const total = users.length;
  const totalPages = Math.ceil(total / limit);
  const startIndex = (page - 1) * limit;
  const endIndex = startIndex + limit;
  const paginatedUsers = users.slice(startIndex, endIndex);

  return {
    users: paginatedUsers,
    pagination: {
      page,
      limit,
      total,
      totalPages,
    },
  };
}

export const userManagementRoutes = {
  // GET /api/users - List users with pagination and filtering
  "/api/users": {
    GET: async (req: Request) => {
      try {
        const url = new URL(req.url);
        const queryParams = Object.fromEntries(url.searchParams);
        
        // Parse and validate query parameters
        const query = UserListQuerySchema.parse({
          page: queryParams.page ? parseInt(queryParams.page) : 1,
          limit: queryParams.limit ? parseInt(queryParams.limit) : 10,
          search: queryParams.search || undefined,
          role: queryParams.role || undefined,
          status: queryParams.status || undefined,
        });

        // Filter users based on query
        const filteredUsers = filterUsers(query);
        
        // Paginate results
        const result = paginateUsers(filteredUsers, query.page, query.limit);
        
        // Validate response
        const validatedResult = UserListResponseSchema.parse(result);
        
        return Response.json(validatedResult);
      } catch (error) {
        console.error('Error fetching users:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    },

    // POST /api/users - Create a new user
    POST: async (req: Request) => {
      try {
        const body = await req.json();
        const userData = UserCreateSchema.parse(body);
        
        // Check if email already exists
        const existingUser = users.find(user => user.email === userData.email);
        if (existingUser) {
          return Response.json(
            { error: 'Email already exists' }, 
            { status: 400 }
          );
        }

        // Create new user
        const newUser: UserManagement = {
          id: nextId++,
          ...userData,
          createdAt: new Date(),
          updatedAt: new Date(),
        };

        users.push(newUser);
        
        const validatedUser = UserManagementSchema.parse(newUser);
        return Response.json(validatedUser, { status: 201 });
      } catch (error) {
        console.error('Error creating user:', error);
        if (error instanceof Error && error.message.includes('validation')) {
          return Response.json(
            { error: 'Invalid user data' }, 
            { status: 400 }
          );
        }
        return new Response('Internal Server Error', { status: 500 });
      }
    },
  },

  // GET /api/users/:id - Get a specific user
  "/api/users/:id": {
    GET: async (req: any) => {
      try {
        const userId = parseInt(req.params.id);
        const user = users.find(u => u.id === userId);
        
        if (!user) {
          return Response.json(
            { error: 'User not found' }, 
            { status: 404 }
          );
        }
        
        const validatedUser = UserManagementSchema.parse(user);
        return Response.json(validatedUser);
      } catch (error) {
        console.error('Error fetching user:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    },

    // PUT /api/users/:id - Update a user
    PUT: async (req: any) => {
      try {
        const userId = parseInt(req.params.id);
        const body = await req.json();
        const updates = UserUpdateSchema.parse(body);
        
        const userIndex = users.findIndex(u => u.id === userId);
        if (userIndex === -1) {
          return Response.json(
            { error: 'User not found' }, 
            { status: 404 }
          );
        }

        // Check if email already exists (if email is being updated)
        if (updates.email && updates.email !== users[userIndex].email) {
          const existingUser = users.find(user => user.email === updates.email);
          if (existingUser) {
            return Response.json(
              { error: 'Email already exists' }, 
              { status: 400 }
            );
          }
        }

        // Update user
        users[userIndex] = {
          ...users[userIndex],
          ...updates,
          updatedAt: new Date(),
        };
        
        const validatedUser = UserManagementSchema.parse(users[userIndex]);
        return Response.json(validatedUser);
      } catch (error) {
        console.error('Error updating user:', error);
        if (error instanceof Error && error.message.includes('validation')) {
          return Response.json(
            { error: 'Invalid user data' }, 
            { status: 400 }
          );
        }
        return new Response('Internal Server Error', { status: 500 });
      }
    },

    // DELETE /api/users/:id - Delete a user (soft delete by setting status to inactive)
    DELETE: async (req: any) => {
      try {
        const userId = parseInt(req.params.id);
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex === -1) {
          return Response.json(
            { error: 'User not found' }, 
            { status: 404 }
          );
        }

        // Soft delete by setting status to inactive
        users[userIndex] = {
          ...users[userIndex],
          status: 'inactive',
          updatedAt: new Date(),
        };
        
        return Response.json({ message: 'User deleted successfully' });
      } catch (error) {
        console.error('Error deleting user:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    },
  },

  // PATCH /api/users/:id/status - Toggle user status
  "/api/users/:id/status": {
    PATCH: async (req: any) => {
      try {
        const userId = parseInt(req.params.id);
        const userIndex = users.findIndex(u => u.id === userId);
        
        if (userIndex === -1) {
          return Response.json(
            { error: 'User not found' }, 
            { status: 404 }
          );
        }

        // Toggle status
        const newStatus = users[userIndex].status === 'active' ? 'inactive' : 'active';
        users[userIndex] = {
          ...users[userIndex],
          status: newStatus,
          updatedAt: new Date(),
        };
        
        const result = UserStatusToggleSchema.parse({
          id: userId,
          status: newStatus,
        });
        
        return Response.json(result);
      } catch (error) {
        console.error('Error toggling user status:', error);
        return new Response('Internal Server Error', { status: 500 });
      }
    },
  },
};
