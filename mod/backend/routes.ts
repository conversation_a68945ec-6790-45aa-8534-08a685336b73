import { connection } from '@/base/db';
import { logger } from '@/base/dual/logger';
import { derive } from '@pumped-fn/core-next';
import { impl } from "@pumped-fn/extra";
import { BunRequest } from 'bun';
import { eq } from 'drizzle-orm';
import { custom } from 'zod/v4-mini';
import { endpoint } from '@/mod/dual/rpc';
import { todos, users } from '@/mod/backend/schema';

const bunContext = custom<BunRequest<string>>()
const router = impl.service(endpoint).context(bunContext)

export const echoRoute = router.implements(
  'echo',
  ({ input }) => {
    return input
  }
)

export const createUserRoute = router.implements(
  'createUser',
  derive(
    [connection, logger('route.createUser')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Creating user with input:', input);

      await db.insert(users).values(input)    

    }
  )
)

export const createTodoRoute = router.implements(
  'createTodo',
  derive(
    [connection, logger('route.createTodo')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Creating todo with input:', input);

      await db.insert(todos).values(input)    

    }
  )
)

export const markTodoAsDoneRoute = router.implements(
  'markTodoAsDone',
  derive(
    [connection, logger('route.markTodoAsDone')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Marking todo as done with input:', input);

      await db.update(todos)
        .set({ completed: true })
        .where(eq(todos.id, input))
    }
  )
)

export const markTodoAsUnDoneRoute = router.implements(
  'markTodoAsUnDone',
  derive(
    [connection, logger('route.markTodoAsUnDone')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Marking todo as undone with input:', input);
      await db.update(todos)
        .set({ completed: false })
        .where(eq(todos.id, input))
    }
  )
)

export const deleteTodoRoute = router.implements(
  'deleteTodo',
  derive(
    [connection, logger('route.deleteTodo')],
    async ([db, logger]) => async ({ input }) => {
      logger.debug('Deleting todo with input:', input);

      await db.delete(todos).where(eq(todos.id, input))
    }
  )
)