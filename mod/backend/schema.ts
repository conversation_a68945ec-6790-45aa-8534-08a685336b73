import { relations, SQL } from "drizzle-orm";
import { pgTable, serial, text, timestamp, boolean, index, unique, PgDialect } from "drizzle-orm/pg-core";

export const users = pgTable("users", {
  id: serial("id").notNull().primaryKey(),
  email: text("email").notNull().unique(),
  name: text("name").notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  unique("users_email_key").on(table.email),
  index("users_email_idx").on(table.email),
  index("users_id_idx").on(table.id),
]);

export const todos = pgTable("todos", {
  id: serial("id").primaryKey().notNull(),
  userId: serial("user_id").notNull(),
  title: text("title").notNull(),
  description: text("description"),
  completed: boolean("completed").default(false).notNull(),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
}, (table) => [
  index("todos_user_id_idx").on(table.userId),
]);

export const userRelations = relations(users, ({ many }) => ({
  todos: many(todos)
}))

export const todoRelations = relations(todos, ({ one }) => ({
  user: one(users, {
    fields: [todos.userId],
    references: [users.id],
  }),
}));

export const toSql = (sql: SQL<unknown>): string => {
  const dialect = new PgDialect()

  return dialect.sqlToQuery(sql).sql
}