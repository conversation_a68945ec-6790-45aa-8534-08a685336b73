/* generated code */
import { z } from "zod/v4";

export const UserSchema = z.object({
  id: z.number(),
  email: z.string().email(),
  name: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const UserInsertSchema = z.object({
  email: z.string().email(),
  name: z.string(),
});

export const TodoSchema = z.object({
  id: z.number(),
  userId: z.number(),
  title: z.string(),
  description: z.string().nullable(),
  completed: z.boolean(),
  status: z.enum(['todo', 'in-progress', 'done']),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const TodoInsertSchema = z.object({
  userId: z.number(),
  title: z.string(),
  description: z.string().nullable(),
  completed: z.boolean().default(false),
  status: z.enum(['todo', 'in-progress', 'done']).default('todo'),
});

export type User = z.input<typeof UserSchema>;
export type UserInsert = z.input<typeof UserInsertSchema>;
export type Todo = z.input<typeof TodoSchema>;
export type TodoInsert = z.input<typeof TodoInsertSchema>;
