import { define } from "@pumped-fn/extra"
import * as schema from "./index.types.zod"
import { z } from "zod/v4"

export const userEndpoint = define.service({
  createUser: {
    input: schema.UserInsertSchema,
    output: z.void(),
  }
})

export const todoEndpoint = define.service({
  createTodo: {
    input: schema.TodoInsertSchema,
    output: z.void(),
  },
  markTodoAsDone: {
    input: z.number(),
    output: z.void(),
  },
  markTodoAsUnDone: {
    input: z.number(),
    output: z.void(),
  },
  deleteTodo: {
    input: z.number(),
    output: z.void(),
  },
})

export const devEndpoint = define.service({
  echo: {
    input: z.any(),
    output: z.any(),
  }
})

export const endpoint = define.service({
  ...userEndpoint,
  ...todoEndpoint,
  ...devEndpoint
})