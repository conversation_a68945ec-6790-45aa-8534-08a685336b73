/* User Management Types and Schemas */
import { z } from "zod/v4";

export const UserManagementSchema = z.object({
  id: z.number(),
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  role: z.enum(['admin', 'user', 'moderator']),
  status: z.enum(['active', 'inactive']),
  avatar: z.string().url().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const UserCreateSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
  role: z.enum(['admin', 'user', 'moderator']).default('user'),
  status: z.enum(['active', 'inactive']).default('active'),
  avatar: z.string().url("Invalid URL").optional(),
});

export const UserUpdateSchema = UserCreateSchema.partial();

export const UserListQuerySchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(10),
  search: z.string().optional(),
  role: z.enum(['admin', 'user', 'moderator']).optional(),
  status: z.enum(['active', 'inactive']).optional(),
});

export const UserListResponseSchema = z.object({
  users: z.array(UserManagementSchema),
  pagination: z.object({
    page: z.number(),
    limit: z.number(),
    total: z.number(),
    totalPages: z.number(),
  }),
});

export const UserStatusToggleSchema = z.object({
  id: z.number(),
  status: z.enum(['active', 'inactive']),
});

// Export types
export type UserManagement = z.infer<typeof UserManagementSchema>;
export type UserCreate = z.infer<typeof UserCreateSchema>;
export type UserUpdate = z.infer<typeof UserUpdateSchema>;
export type UserListQuery = z.infer<typeof UserListQuerySchema>;
export type UserListResponse = z.infer<typeof UserListResponseSchema>;
export type UserStatusToggle = z.infer<typeof UserStatusToggleSchema>;

// Role and status options for UI
export const USER_ROLES = [
  { value: 'admin', label: 'Administrator' },
  { value: 'user', label: 'User' },
  { value: 'moderator', label: 'Moderator' },
] as const;

export const USER_STATUSES = [
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
] as const;
